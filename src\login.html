<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - كيما</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #2D5016 0%, #4a7c23 100%);
            min-height: 100vh;
        }
        
        .login-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }
        
        .logo-animation {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .btn-login {
            background: linear-gradient(135deg, #2D5016 0%, #4a7c23 100%);
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(45, 80, 22, 0.3);
        }
        
        .input-field {
            transition: all 0.3s ease;
            outline: none;
        }

        .input-field:focus {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(45, 80, 22, 0.15);
            outline: none !important;
            border-color: #4a7c23;
        }

        /* إزالة جميع الإطارات الافتراضية */
        input:focus {
            outline: none !important;
            box-shadow: none !important;
        }

        input:focus-visible {
            outline: none !important;
        }
    </style>
</head>
<body class="flex items-center justify-center p-4">
    <div class="login-card rounded-2xl p-8 w-full max-w-md">
        <!-- الشعار والعنوان -->
        <div class="text-center mb-8">
            <div class="logo-animation mb-6">
                <img src="../assets/kima-logo.svg" alt="شعار كيما" class="w-24 h-24 mx-auto">
            </div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">كيما</h1>
            <h2 class="text-lg font-semibold text-green-700 mb-1">الصناعات الكيماوية المصرية</h2>
            <p class="text-gray-600 text-sm">نظام إدارة المخازن</p>
        </div>

        <!-- نموذج تسجيل الدخول -->
        <form id="loginForm" class="space-y-6">
            <!-- اسم المستخدم -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-user ml-2"></i>
                    اسم المستخدم
                </label>
                <input
                    type="text"
                    id="username"
                    class="input-field w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-green-600 outline-none"
                    placeholder="أدخل اسم المستخدم"
                    required
                >
            </div>

            <!-- كلمة المرور -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-lock ml-2"></i>
                    كلمة المرور
                </label>
                <div class="relative">
                    <input
                        type="password"
                        id="password"
                        class="input-field w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:border-green-600 outline-none"
                        placeholder="أدخل كلمة المرور"
                        required
                    >
                    <button
                        type="button"
                        id="togglePassword"
                        class="absolute left-3 top-3 text-gray-500 hover:text-gray-700 transition-colors"
                        title="إظهار كلمة المرور"
                    >
                        <i class="fas fa-eye-slash"></i>
                    </button>
                </div>
            </div>

            <!-- رسالة الخطأ -->
            <div id="errorMessage" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
                <i class="fas fa-exclamation-triangle ml-2"></i>
                <span id="errorText">بيانات تسجيل الدخول غير صحيحة</span>
            </div>

            <!-- تذكرني -->
            <div class="flex items-center justify-between">
                <label class="flex items-center cursor-pointer">
                    <input type="checkbox" id="rememberMe" class="rounded border-gray-300 text-green-600 focus:ring-green-500 focus:ring-2">
                    <span class="mr-2 text-sm text-gray-600 hover:text-gray-800 transition-colors">
                        <i class="fas fa-user-check ml-1"></i>
                        تذكرني
                    </span>
                </label>
                <a href="#" id="forgotPassword" class="text-sm text-green-600 hover:text-green-800 transition-colors">
                    <i class="fas fa-key ml-1"></i>
                    نسيت كلمة المرور؟
                </a>
            </div>

            <!-- زر تسجيل الدخول -->
            <button 
                type="submit" 
                id="loginBtn"
                class="btn-login w-full text-white py-3 px-4 rounded-lg font-semibold text-lg"
            >
                <i class="fas fa-sign-in-alt ml-2"></i>
                <span id="loginBtnText">تسجيل الدخول</span>
                <i id="loginSpinner" class="fas fa-spinner fa-spin mr-2 hidden"></i>
            </button>
        </form>

        <!-- معلومات إضافية -->
        <div class="mt-8 text-center">
            <p class="text-xs text-gray-500">
                © 2024 شركة الصناعات الكيماوية المصرية - كيما
            </p>
            <p class="text-xs text-gray-400 mt-1">
                الإصدار 1.0.0
            </p>
        </div>
    </div>

    <!-- تحميل JavaScript -->
    <script src="login.js"></script>
</body>
</html>
