<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - كيما</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }

        .login-container {
            background: linear-gradient(135deg, #2D5016 0%, #4a7c23 100%);
            min-height: 100vh;
        }

        .login-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .logo-container {
            animation: logoFloat 3s ease-in-out infinite;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .input-group {
            position: relative;
        }

        .input-group input:focus + label,
        .input-group input:not(:placeholder-shown) + label {
            transform: translateY(-25px) scale(0.8);
            color: #2D5016;
        }

        .input-group label {
            position: absolute;
            right: 12px;
            top: 12px;
            transition: all 0.3s ease;
            pointer-events: none;
            color: #6b7280;
        }

        .input-group input {
            position: relative;
        }

        .input-group button {
            position: absolute;
            z-index: 10;
        }

        .login-btn {
            background: linear-gradient(135deg, #2D5016 0%, #4a7c23 100%);
            transition: all 0.3s ease;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(45, 80, 22, 0.3);
        }

        .error-message {
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    </style>
</head>
<body class="login-container flex items-center justify-center p-4">
    <div class="login-card rounded-2xl p-8 w-full max-w-md">
        <!-- الشعار والعنوان -->
        <div class="text-center mb-8">
            <div class="logo-container mb-4">
                <img src="../assets/kima-logo.svg" alt="شعار كيما" class="w-20 h-20 mx-auto">
            </div>
            <h1 class="text-2xl font-bold text-gray-800 mb-2">شركة الصناعات الكيماوية المصرية</h1>
            <h2 class="text-xl font-semibold text-green-700 mb-1">كيما</h2>
            <p class="text-gray-600 text-sm">نظام إدارة المخازن</p>
        </div>

        <!-- نموذج تسجيل الدخول -->
        <form id="loginForm" class="space-y-6">
            <!-- اسم المستخدم -->
            <div class="input-group">
                <input
                    type="text"
                    id="username"
                    name="username"
                    placeholder=" "
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none transition-all"
                    required
                >
                <label for="username" class="text-gray-500">
                    <i class="fas fa-user ml-2"></i>
                    اسم المستخدم
                </label>
            </div>

            <!-- كلمة المرور -->
            <div class="input-group">
                <input
                    type="password"
                    id="password"
                    name="password"
                    placeholder=" "
                    class="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none transition-all"
                    required
                >
                <label for="password" class="text-gray-500">
                    <i class="fas fa-lock ml-2"></i>
                    كلمة المرور
                </label>
                <button
                    type="button"
                    id="togglePassword"
                    class="absolute left-3 top-3 text-gray-500 hover:text-gray-700 transition-colors"
                >
                    <i class="fas fa-eye"></i>
                </button>
            </div>

            <!-- رسالة الخطأ -->
            <div id="errorMessage" class="hidden error-message bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
                <i class="fas fa-exclamation-triangle ml-2"></i>
                <span id="errorText">بيانات تسجيل الدخول غير صحيحة</span>
            </div>

            <!-- تذكرني -->
            <div class="flex items-center justify-between">
                <label class="flex items-center">
                    <input type="checkbox" id="rememberMe" class="rounded border-gray-300 text-green-600 focus:ring-green-500">
                    <span class="mr-2 text-sm text-gray-600">تذكرني</span>
                </label>
                <a href="#" id="forgotPassword" class="text-sm text-green-600 hover:text-green-800 transition-colors">تغيير كلمة المرور</a>
            </div>

            <!-- زر تسجيل الدخول -->
            <button
                type="submit"
                id="loginBtn"
                class="login-btn w-full text-white py-3 px-4 rounded-lg font-semibold text-lg"
            >
                <i class="fas fa-sign-in-alt ml-2"></i>
                <span id="loginBtnText">تسجيل الدخول</span>
                <i id="loginSpinner" class="fas fa-spinner fa-spin mr-2 hidden"></i>
            </button>
        </form>



        <!-- معلومات إضافية -->
        <div class="mt-4 text-center">
            <p class="text-xs text-gray-500">
                © 2024 شركة الصناعات الكيماوية المصرية - كيما
            </p>
            <p class="text-xs text-gray-400 mt-1">
                الإصدار 1.0.0
            </p>
        </div>
    </div>

    <!-- تحميل JavaScript -->
    <script src="login.js"></script>
</body>
</html>
