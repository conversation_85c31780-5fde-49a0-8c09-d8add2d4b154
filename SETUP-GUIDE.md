# دليل الإعداد السريع - تطبيق مخازن كيما

## 🚀 البدء السريع

### الطريقة الأولى: التشغيل المباشر (الأسهل)
1. **النقر المزدوج على `start.bat`**
   - سيقوم بتثبيت التبعيات تلقائياً
   - سيشغل التطبيق مباشرة

### الطريقة الثانية: استخدام سطر الأوامر
```bash
# تثبيت التبعيات
npm install

# تشغيل التطبيق
npm start
```

## 📋 متطلبات النظام

- **نظام التشغيل:** Windows 7/10/11
- **الذاكرة:** 4 جيجابايت رام (الحد الأدنى)
- **المساحة:** 200 ميجابايت
- **Node.js:** الإصدار 16 أو أحدث

## 🔧 إعداد الأيقونات

### إنشاء ملفات PNG من SVG:
1. افتح `assets/convert-svg-to-png.html` في المتصفح
2. انقر على أزرار التحميل
3. احفظ الملفات في مجلد `assets` بالأسماء:
   - `kima-logo.png` (للشعار)
   - `icon.png` (للأيقونة)

### إنشاء ملف ICO لنظام Windows:
```bash
# باستخدام ImageMagick
magick icon.png icon.ico

# أو استخدم أدوات أونلاين مثل:
# - convertio.co
# - cloudconvert.com
```

## 🏗️ بناء التطبيق للتوزيع

```bash
# بناء لنظام Windows
npm run build-win

# بناء عام
npm run build
```

الملفات المُنشأة ستكون في مجلد `dist/`

## 🎯 الميزات الرئيسية

### ✅ واجهة عربية كاملة
- تصميم RTL احترافي
- خطوط عربية أنيقة (Cairo)
- ألوان متناسقة مع هوية كيما

### ✅ إدارة المخزون
- إضافة وتعديل وحذف الأصناف
- بحث ديناميكي فوري
- تتبع المواقع (الشلف والعين)
- إدارة بيانات الموردين

### ✅ تصدير واستيراد
- تصدير إلى Excel
- استيراد من Excel
- نسخ احتياطية JSON

### ✅ إحصائيات مفيدة
- إجمالي الأصناف
- الأصناف منخفضة المخزون
- عدد الموردين
- الأصناف الحديثة

## 🗂️ هيكل المشروع

```
kima-warehouse-app/
├── main.js                 # الملف الرئيسي لـ Electron
├── preload.js             # الواجهة الآمنة
├── package.json           # إعدادات المشروع
├── start.bat             # ملف التشغيل السريع
├── src/
│   ├── index.html        # الواجهة الرئيسية
│   ├── styles.css        # التنسيقات المخصصة
│   ├── renderer.js       # منطق الواجهة
│   ├── database.js       # إدارة قاعدة البيانات
│   └── backup.js         # نظام النسخ الاحتياطي
├── assets/
│   ├── icon.svg          # أيقونة التطبيق (SVG)
│   ├── kima-logo.svg     # شعار كيما (SVG)
│   └── convert-svg-to-png.html  # أداة تحويل الأيقونات
└── docs/
    ├── README.md         # دليل المستخدم
    ├── INSTALL.md        # دليل التثبيت
    └── SETUP-GUIDE.md    # هذا الملف
```

## 🔍 استكشاف الأخطاء

### مشكلة: "node is not recognized"
**الحل:**
1. تثبيت Node.js من https://nodejs.org/
2. إعادة تشغيل Command Prompt
3. تشغيل `start.bat` مرة أخرى

### مشكلة: "npm install fails"
**الحل:**
```bash
# مسح cache
npm cache clean --force

# حذف node_modules وإعادة التثبيت
rmdir /s node_modules
npm install
```

### مشكلة: التطبيق لا يفتح
**الحل:**
1. تحقق من وجود جميع الملفات
2. تشغيل في وضع التطوير:
```bash
npm run dev
```

### مشكلة: الشعار لا يظهر
**الحل:**
1. تأكد من وجود `assets/kima-logo.svg`
2. أو أنشئ ملف PNG باستخدام `convert-svg-to-png.html`

## 📱 استخدام التطبيق

### إضافة صنف جديد:
1. انقر "➕ إضافة صنف جديد"
2. املأ البيانات المطلوبة
3. انقر "حفظ"

### البحث:
- اكتب في حقل البحث أعلى الصفحة
- النتائج تظهر فوراً

### التصدير:
- انقر "تصدير Excel"
- اختر مكان الحفظ

### الاستيراد:
- انقر "استيراد Excel"
- اختر ملف Excel
- تأكد من وجود الأعمدة المطلوبة

### النسخ الاحتياطي:
- انقر "نسخ احتياطي"
- سيتم تحميل ملف JSON

## 🔐 الأمان والخصوصية

- ✅ جميع البيانات محفوظة محلياً
- ✅ لا يتم إرسال بيانات عبر الإنترنت
- ✅ النسخ الاحتياطية آمنة
- ✅ لا توجد تتبع أو إعلانات

## 📞 الدعم الفني

للحصول على المساعدة:
1. مراجعة هذا الدليل
2. فحص ملف README.md
3. التواصل مع فريق تطوير كيما

## 🎉 تهانينا!

تم إعداد تطبيق مخازن كيما بنجاح! 

**الخطوات التالية:**
1. تشغيل التطبيق باستخدام `start.bat`
2. إضافة أول صنف للاختبار
3. تجربة ميزات البحث والتصدير
4. إنشاء نسخة احتياطية

---

**شركة الصناعات الكيماوية المصرية - كيما**  
*نظام إدارة المخازن الذكي*
