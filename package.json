{"name": "kima-warehouse-app", "version": "1.0.0", "description": "تطبيق مخازن شركة الصناعات الكيماوية المصرية - كيما", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "dist": "electron-builder --publish=never"}, "keywords": ["electron", "warehouse", "inventory", "kima", "arabic"], "author": "شركة الصناعات الكيماوية المصرية - كيما", "license": "MIT", "devDependencies": {"electron": "^27.3.11", "electron-builder": "^24.6.4"}, "dependencies": {"electron-store": "^8.1.0", "xlsx": "^0.18.5"}, "build": {"appId": "com.kima.warehouse", "productName": "كيما - إدارة المخازن", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "src/**/*", "assets/**/*", "node_modules/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "assets/kima-logo.svg"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "كيما - إدارة المخازن"}}}