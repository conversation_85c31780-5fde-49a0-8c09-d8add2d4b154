// منطق الواجهة الرئيسية
class WarehouseApp {
    constructor() {
        this.currentEditingId = null;
        this.searchTimeout = null;
        this.selectedItemId = null;
        this.timeInterval = null;
        this.currentUser = null;

        // التحقق من تسجيل الدخول
        if (!this.checkAuthentication()) {
            window.location.href = 'login.html';
            return;
        }

        this.init();
    }

    // التحقق من تسجيل الدخول
    checkAuthentication() {
        const session = localStorage.getItem('kimaUserSession');
        if (!session) {
            return false;
        }

        try {
            this.currentUser = JSON.parse(session);
            this.displayUserInfo();
            return true;
        } catch (error) {
            console.error('خطأ في قراءة بيانات المستخدم:', error);
            localStorage.removeItem('kimaUserSession');
            return false;
        }
    }

    // عرض معلومات المستخدم
    displayUserInfo() {
        if (this.currentUser) {
            // إضافة معلومات المستخدم للهيدر
            const userInfo = document.createElement('div');
            userInfo.className = 'flex items-center space-x-3 space-x-reverse text-sm';
            userInfo.innerHTML = `
                <div class="text-right">
                    <div class="text-white font-medium">${this.currentUser.fullName}</div>
                    <div class="text-green-100 text-xs">${this.currentUser.role}</div>
                </div>
                <button
                    onclick="app.logout()"
                    class="bg-white bg-opacity-20 hover:bg-opacity-30 text-white px-3 py-1 rounded-lg text-xs transition-all"
                    title="تسجيل الخروج"
                >
                    <i class="fas fa-sign-out-alt ml-1"></i>
                    خروج
                </button>
            `;

            // إضافة معلومات المستخدم للهيدر
            const headerRight = document.querySelector('header .flex.items-center.space-x-4.space-x-reverse:last-child');
            if (headerRight) {
                headerRight.insertBefore(userInfo, headerRight.firstChild);
            }
        }
    }

    // تسجيل الخروج
    logout() {
        const confirmed = confirm('هل أنت متأكد من تسجيل الخروج؟');
        if (confirmed) {
            localStorage.removeItem('kimaUserSession');
            sessionStorage.removeItem('kimaCurrentUser');
            window.location.href = 'login.html';
        }
    }

    async init() {
        try {
            // انتظار تحميل قاعدة البيانات
            await dbManager.init();

            // إعداد الأحداث
            this.setupEventListeners();

            // تحميل البيانات الأولية
            await this.loadData();

            // تحديث التاريخ والوقت
            this.updateCurrentDate();
            this.updateCurrentTime();
            this.startTimeUpdater();

            console.log('تم تحميل التطبيق بنجاح');
        } catch (error) {
            console.error('خطأ في تحميل التطبيق:', error);
            this.showNotification('خطأ في تحميل التطبيق', 'error');
        }
    }

    setupEventListeners() {
        // البحث الديناميكي
        const searchInput = document.getElementById('searchInput');
        searchInput.addEventListener('input', (e) => {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.performSearch(e.target.value);
            }, 300);
        });

        // أزرار الإجراءات الرئيسية
        document.getElementById('addItemBtn').addEventListener('click', () => this.openAddModal());
        document.getElementById('exportBtn').addEventListener('click', () => this.exportToExcel());
        document.getElementById('importBtn').addEventListener('click', () => this.importFromExcel());

        // البحث المتقدم
        document.getElementById('advancedSearchBtn').addEventListener('click', () => this.openAdvancedSearch());
        document.getElementById('closeAdvancedSearch').addEventListener('click', () => this.closeAdvancedSearch());
        document.getElementById('cancelAdvancedSearch').addEventListener('click', () => this.closeAdvancedSearch());
        document.getElementById('advancedSearchForm').addEventListener('submit', (e) => this.handleAdvancedSearch(e));
        document.getElementById('clearAdvancedSearch').addEventListener('click', () => this.clearAdvancedSearchForm());
        document.getElementById('resetSearchBtn').addEventListener('click', () => this.resetSearch());

        // نافذة بيان الصرف
        document.getElementById('closeDispensingModal').addEventListener('click', () => this.closeDispensingModal());
        document.getElementById('cancelDispensingUpdate').addEventListener('click', () => this.clearDispensingForm());
        document.getElementById('dispensingUpdateForm').addEventListener('submit', (e) => this.handleDispensingUpdate(e));

        // نافذة إضافة/تعديل الصنف
        document.getElementById('closeModal').addEventListener('click', () => this.closeModal());
        document.getElementById('cancelBtn').addEventListener('click', () => this.closeModal());
        document.getElementById('itemForm').addEventListener('submit', (e) => this.handleFormSubmit(e));

        // إغلاق النافذة بالنقر خارجها
        document.getElementById('itemModal').addEventListener('click', (e) => {
            if (e.target.id === 'itemModal') {
                this.closeModal();
            }
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
                this.hideContextMenu();
            } else if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                this.openAddModal();
            } else if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                searchInput.focus();
            }
        });

        // قائمة السياق
        document.getElementById('editContextBtn').addEventListener('click', () => {
            if (this.selectedItemId) {
                this.editItem(this.selectedItemId);
                this.hideContextMenu();
            }
        });

        document.getElementById('deleteContextBtn').addEventListener('click', () => {
            if (this.selectedItemId) {
                this.deleteItem(this.selectedItemId);
                this.hideContextMenu();
            }
        });

        // إخفاء قائمة السياق عند النقر في أي مكان آخر
        document.addEventListener('click', (e) => {
            if (!e.target.closest('#contextMenu')) {
                this.hideContextMenu();
            }
        });
    }

    async loadData() {
        try {
            // تحميل جميع الأصناف
            const items = await dbManager.getAllItems();
            this.displayItems(items);

            // تحديث الإحصائيات
            await this.updateStatistics();
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
            this.showNotification('خطأ في تحميل البيانات', 'error');
        }
    }

    displayItems(items) {
        const tbody = document.getElementById('itemsTableBody');
        const noDataMessage = document.getElementById('noDataMessage');

        if (items.length === 0) {
            tbody.innerHTML = '';
            noDataMessage.classList.remove('hidden');
            return;
        }

        noDataMessage.classList.add('hidden');

        tbody.innerHTML = items.map((item, index) => `
            <tr class="hover:bg-gray-50 transition-colors cursor-pointer"
                data-item-id="${item.id}"
                oncontextmenu="app.showContextMenu(event, ${item.id})"
                ondblclick="app.editItem(${item.id})">
                <td class="px-6 py-4 text-sm text-center font-medium text-gray-500">${index + 1}</td>
                <td class="px-6 py-4 text-sm text-gray-900">${this.escapeHtml(item.name || '')}</td>
                <td class="px-6 py-4 text-sm font-medium text-blue-600">${this.escapeHtml(item.itemCode || '')}</td>
                <td class="px-6 py-4 text-sm text-gray-600">${this.escapeHtml(item.specs || '')}</td>
                <td class="px-6 py-4 text-sm font-medium text-gray-900">${this.escapeHtml(item.code || '')}</td>
                <td class="px-6 py-4 text-sm text-gray-900">
                    <span class="px-2 py-1 rounded-full text-xs font-medium ${this.getStockStatusClass(item.stock)}">
                        ${item.stock || 0}
                    </span>
                </td>
                <td class="px-6 py-4 text-sm text-gray-600">${this.escapeHtml(item.shelfNumber || '')}</td>
                <td class="px-6 py-4 text-sm text-gray-600">${this.escapeHtml(item.eyeNumber || '')}</td>
                <td class="px-6 py-4 text-sm text-gray-600">${this.escapeHtml(item.supplierName || '')}</td>
                <td class="px-6 py-4 text-sm text-gray-600">${this.formatDate(item.supplyDate)}</td>
                <td class="px-6 py-4 text-center">
                    <button
                        onclick="app.openDispensingModal('${item.id}')"
                        class="dispensing-btn text-white px-3 py-1 rounded-lg text-sm flex items-center space-x-1 space-x-reverse mx-auto"
                        title="عرض وإدارة بيان الصرف"
                    >
                        <i class="fas fa-clipboard-list"></i>
                        <span>بيان الصرف</span>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    getStockStatusClass(stock) {
        const stockNum = parseInt(stock) || 0;
        if (stockNum === 0) return 'bg-red-100 text-red-800';
        if (stockNum < 10) return 'bg-yellow-100 text-yellow-800';
        return 'bg-green-100 text-green-800';
    }

    async updateStatistics() {
        try {
            const stats = await dbManager.getStatistics();

            document.getElementById('totalItems').textContent = stats.totalItems;
            document.getElementById('totalSuppliers').textContent = stats.totalSuppliers;
        } catch (error) {
            console.error('خطأ في تحديث الإحصائيات:', error);
        }
    }

    async performSearch(query) {
        try {
            const items = await dbManager.searchItems(query);
            this.displayItems(items);
        } catch (error) {
            console.error('خطأ في البحث:', error);
            this.showNotification('خطأ في البحث', 'error');
        }
    }

    openAddModal() {
        this.currentEditingId = null;
        document.getElementById('modalTitle').textContent = 'إضافة صنف جديد';
        document.getElementById('itemForm').reset();
        document.getElementById('itemModal').classList.remove('hidden');
        document.getElementById('itemName').focus();
    }

    async editItem(id) {
        try {
            const item = await dbManager.getItem(id);
            if (!item) {
                this.showNotification('الصنف غير موجود', 'error');
                return;
            }

            this.currentEditingId = id;
            document.getElementById('modalTitle').textContent = 'تعديل الصنف';

            // ملء النموذج بالبيانات
            document.getElementById('itemName').value = item.name || '';
            document.getElementById('itemCodeField').value = item.itemCode || '';
            document.getElementById('itemSpecs').value = item.specs || '';
            document.getElementById('itemCode').value = item.code || '';
            document.getElementById('itemStock').value = item.stock || '';
            document.getElementById('shelfNumber').value = item.shelfNumber || '';
            document.getElementById('eyeNumber').value = item.eyeNumber || '';
            document.getElementById('dispensingInfo').value = item.dispensingInfo || '';
            document.getElementById('supplierName').value = item.supplierName || '';
            document.getElementById('supplyDate').value = item.supplyDate || '';

            document.getElementById('itemModal').classList.remove('hidden');
            document.getElementById('itemName').focus();
        } catch (error) {
            console.error('خطأ في تحميل بيانات الصنف:', error);
            this.showNotification('خطأ في تحميل بيانات الصنف', 'error');
        }
    }

    async deleteItem(id) {
        try {
            const confirmed = confirm('هل أنت متأكد من حذف هذا الصنف؟');
            if (!confirmed) return;

            await dbManager.deleteItem(id);
            await this.loadData();
            this.showNotification('تم حذف الصنف بنجاح', 'success');
        } catch (error) {
            console.error('خطأ في حذف الصنف:', error);
            this.showNotification('خطأ في حذف الصنف', 'error');
        }
    }

    closeModal() {
        document.getElementById('itemModal').classList.add('hidden');
        this.currentEditingId = null;
    }

    async handleFormSubmit(e) {
        e.preventDefault();

        try {
            const formData = new FormData(e.target);
            const item = {
                name: document.getElementById('itemName').value.trim(),
                itemCode: document.getElementById('itemCodeField').value.trim(),
                specs: document.getElementById('itemSpecs').value.trim(),
                code: document.getElementById('itemCode').value.trim(),
                stock: parseInt(document.getElementById('itemStock').value) || 0,
                shelfNumber: document.getElementById('shelfNumber').value.trim(),
                eyeNumber: document.getElementById('eyeNumber').value.trim(),
                dispensingInfo: document.getElementById('dispensingInfo').value.trim(),
                supplierName: document.getElementById('supplierName').value.trim(),
                supplyDate: document.getElementById('supplyDate').value
            };

            // التحقق من البيانات المطلوبة
            if (!item.name || !item.itemCode || !item.code) {
                this.showErrorNotification('اسم الصنف والكود ورقم الصنف المخزني مطلوبان');
                return;
            }

            if (this.currentEditingId) {
                // تحديث صنف موجود
                await dbManager.updateItem(this.currentEditingId, item);
                this.showSuccessNotification('تم تحديث الصنف بنجاح');
            } else {
                // إضافة صنف جديد
                await dbManager.addItem(item);
                this.showSuccessNotification('تم إضافة الصنف بنجاح');
            }

            this.closeModal();
            await this.loadData();
        } catch (error) {
            console.error('خطأ في حفظ الصنف:', error);
            if (error.message.includes('unique')) {
                this.showErrorNotification('رقم الصنف موجود مسبقاً');
            } else {
                this.showErrorNotification('خطأ في حفظ الصنف');
            }
        }
    }

    async exportToExcel() {
        try {
            this.showInfoNotification('جاري تصدير البيانات...', 2000);

            // التحقق من وجود مكتبة XLSX
            if (typeof XLSX === 'undefined') {
                // تحميل مكتبة XLSX
                await this.loadXLSXLibrary();
            }

            // الحصول على البيانات
            const items = await dbManager.getAllItems();

            if (items.length === 0) {
                this.showWarningNotification('لا توجد بيانات للتصدير');
                return;
            }

            // تحويل البيانات للتصدير
            const exportData = items.map((item, index) => ({
                'م': index + 1,
                'اسم الصنف': item.name || '',
                'الكود': item.itemCode || '',
                'المواصفات': item.specs || '',
                'رقم الصنف المخزني': item.code || '',
                'الرصيد': item.stock || 0,
                'رقم الشلف': item.shelfNumber || '',
                'رقم العين': item.eyeNumber || '',
                'بيان الصرف': item.dispensingInfo || '',
                'اسم المورد': item.supplierName || '',
                'تاريخ التوريد': item.supplyDate || '',
                'تاريخ الإنشاء': item.createdAt ? new Date(item.createdAt).toLocaleDateString('ar-EG') : '',
                'تاريخ التحديث': item.updatedAt ? new Date(item.updatedAt).toLocaleDateString('ar-EG') : ''
            }));

            // إنشاء ورقة عمل Excel
            const worksheet = XLSX.utils.json_to_sheet(exportData);

            // تحسين عرض الأعمدة
            const columnWidths = [
                { wch: 5 },  // م
                { wch: 25 }, // اسم الصنف
                { wch: 15 }, // الكود
                { wch: 30 }, // المواصفات
                { wch: 15 }, // رقم الصنف المخزني
                { wch: 10 }, // الرصيد
                { wch: 12 }, // رقم الشلف
                { wch: 12 }, // رقم العين
                { wch: 25 }, // بيان الصرف
                { wch: 20 }, // اسم المورد
                { wch: 15 }, // تاريخ التوريد
                { wch: 15 }, // تاريخ الإنشاء
                { wch: 15 }  // تاريخ التحديث
            ];
            worksheet['!cols'] = columnWidths;

            // إنشاء كتاب العمل
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'أصناف المخزن');

            // إضافة معلومات إضافية
            const infoSheet = XLSX.utils.aoa_to_sheet([
                ['تقرير مخازن شركة الصناعات الكيماوية المصرية - كيما'],
                ['تاريخ التصدير:', new Date().toLocaleDateString('ar-EG')],
                ['عدد الأصناف:', exportData.length],
                [''],
                ['ملاحظات:'],
                ['- هذا التقرير تم إنشاؤه تلقائياً من نظام إدارة المخازن'],
                ['- للاستفسارات يرجى التواصل مع إدارة المخازن']
            ]);
            XLSX.utils.book_append_sheet(workbook, infoSheet, 'معلومات التقرير');

            // تحويل إلى buffer وتحميل
            const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

            // إنشاء اسم الملف
            const timestamp = new Date().toISOString().split('T')[0];
            const filename = `كيما-المخازن-${timestamp}.xlsx`;

            // تحميل الملف
            this.downloadFile(excelBuffer, filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

            this.showSuccessNotification(`تم تصدير ${exportData.length} صنف بنجاح إلى ملف Excel`);
        } catch (error) {
            console.error('خطأ في التصدير:', error);
            this.showErrorNotification('خطأ في تصدير البيانات: ' + error.message);
        }
    }

    // تحميل مكتبة XLSX
    loadXLSXLibrary() {
        return new Promise((resolve, reject) => {
            if (typeof XLSX !== 'undefined') {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
            script.onload = () => resolve();
            script.onerror = () => reject(new Error('فشل في تحميل مكتبة Excel'));
            document.head.appendChild(script);
        });
    }

    // تحميل ملف
    downloadFile(data, filename, mimeType) {
        const blob = new Blob([data], { type: mimeType });
        const url = URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // تنظيف الذاكرة
        setTimeout(() => URL.revokeObjectURL(url), 100);
    }

    async importFromExcel() {
        try {
            // التحقق من وجود مكتبة XLSX
            if (typeof XLSX === 'undefined') {
                await this.loadXLSXLibrary();
            }

            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.xlsx,.xls';

            input.onchange = async (e) => {
                const file = e.target.files[0];
                if (!file) return;

                try {
                    this.showNotification('جاري استيراد البيانات...', 'info');

                    // قراءة الملف
                    const arrayBuffer = await this.readFileAsArrayBuffer(file);

                    // استخدام مكتبة XLSX لقراءة البيانات
                    const workbook = XLSX.read(arrayBuffer, { type: 'array' });

                    if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
                        this.showNotification('الملف فارغ أو لا يحتوي على أوراق عمل', 'error');
                        return;
                    }

                    // قراءة الورقة الأولى
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];

                    // تحويل البيانات إلى JSON
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                    if (!jsonData || jsonData.length === 0) {
                        this.showNotification('الملف فارغ', 'warning');
                        return;
                    }

                    // معالجة البيانات
                    const result = await this.processImportedData(jsonData);

                    if (result.success > 0) {
                        await this.loadData();
                        this.showNotification(
                            `تم استيراد ${result.success} صنف بنجاح${result.errors > 0 ? ` مع ${result.errors} خطأ` : ''}`,
                            'success'
                        );
                    } else {
                        this.showNotification('لم يتم استيراد أي بيانات', 'warning');
                    }

                } catch (error) {
                    console.error('خطأ في الاستيراد:', error);
                    this.showNotification('خطأ في استيراد البيانات: ' + error.message, 'error');
                }
            };

            input.click();
        } catch (error) {
            console.error('خطأ في فتح ملف الاستيراد:', error);
            this.showNotification('خطأ في فتح ملف الاستيراد', 'error');
        }
    }

    async readFileAsArrayBuffer(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(new Error('خطأ في قراءة الملف'));
            reader.readAsArrayBuffer(file);
        });
    }

    async processImportedData(data) {
        let successCount = 0;
        let errorCount = 0;
        const errors = [];

        try {
            // تخطي الصف الأول إذا كان يحتوي على عناوين
            const startRow = this.detectHeaderRow(data);

            for (let i = startRow; i < data.length; i++) {
                const row = data[i];

                // تخطي الصفوف الفارغة
                if (!row || row.length === 0 || row.every(cell => !cell)) {
                    continue;
                }

                try {
                    const item = this.parseRowToItem(row, i + 1);

                    if (item) {
                        // التحقق من وجود الصنف بنفس الكود
                        const existingItems = await dbManager.getAllItems();
                        const existingItem = existingItems.find(existing =>
                            existing.code && existing.code.toLowerCase() === item.code.toLowerCase()
                        );

                        if (existingItem) {
                            // تحديث الصنف الموجود
                            await dbManager.updateItem(existingItem.id, item);
                        } else {
                            // إضافة صنف جديد
                            await dbManager.addItem(item);
                        }

                        successCount++;
                    }
                } catch (error) {
                    // تجاهل أخطاء صفوف العناوين
                    if (error.message.includes('هذا صف عناوين')) {
                        console.log(`تم تجاهل صف العناوين في الصف ${i + 1}`);
                        continue;
                    }

                    console.error(`خطأ في الصف ${i + 1}:`, error);
                    errors.push(`الصف ${i + 1}: ${error.message}`);
                    errorCount++;
                }
            }

            return {
                success: successCount,
                errors: errorCount,
                errorDetails: errors
            };

        } catch (error) {
            console.error('خطأ في معالجة البيانات:', error);
            throw error;
        }
    }

    detectHeaderRow(data) {
        // البحث عن صف العناوين في أول 3 صفوف
        for (let i = 0; i < Math.min(3, data.length); i++) {
            const row = data[i];
            if (row && row.length > 0) {
                // فحص جميع خلايا الصف للبحث عن كلمات العناوين
                const rowText = row.map(cell => String(cell || '').toLowerCase()).join(' ');

                // كلمات العناوين المحتملة
                const headerKeywords = [
                    'اسم', 'name', 'صنف', 'item',
                    'كود', 'code', 'رقم', 'number',
                    'مواصفات', 'specs', 'specification',
                    'رصيد', 'stock', 'quantity',
                    'شلف', 'shelf', 'عين', 'eye',
                    'مورد', 'supplier', 'تاريخ', 'date'
                ];

                // إذا وجدت 3 أو أكثر من كلمات العناوين في الصف
                const foundKeywords = headerKeywords.filter(keyword =>
                    rowText.includes(keyword)
                ).length;

                if (foundKeywords >= 3) {
                    console.log(`تم اكتشاف صف العناوين في الصف ${i + 1}`);
                    return i + 1; // تخطي صف العناوين
                }
            }
        }

        console.log('لم يتم اكتشاف صف عناوين، سيتم البدء من الصف الأول');
        return 0; // لا يوجد صف عناوين
    }

    parseRowToItem(row, rowNumber) {
        try {
            // تنسيق البيانات المتوقع:
            // [اسم الصنف, الكود, المواصفات, رقم الصنف المخزني, الرصيد, رقم الشلف, رقم العين, بيان الصرف, اسم المورد, تاريخ التوريد]

            const name = this.cleanString(row[0]);
            const itemCode = this.cleanString(row[1]);
            const specs = this.cleanString(row[2]);
            const code = this.cleanString(row[3]);
            const stock = this.parseNumber(row[4]) || 0;
            const shelfNumber = this.cleanString(row[5]);
            const eyeNumber = this.cleanString(row[6]);
            const dispensingInfo = this.cleanString(row[7]);
            const supplierName = this.cleanString(row[8]);
            const supplyDate = this.cleanString(row[9]);

            // التحقق من أن هذا ليس صف عناوين
            if (this.isHeaderRow(row)) {
                throw new Error('هذا صف عناوين وليس بيانات');
            }

            // التحقق من البيانات المطلوبة
            if (!name || !itemCode || !code) {
                throw new Error('اسم الصنف والكود ورقم الصنف المخزني مطلوبان');
            }

            return {
                name: name,
                itemCode: itemCode,
                specs: specs,
                code: code,
                stock: stock,
                shelfNumber: shelfNumber,
                eyeNumber: eyeNumber,
                dispensingInfo: dispensingInfo,
                supplierName: supplierName,
                supplyDate: supplyDate
            };

        } catch (error) {
            throw new Error(`خطأ في تحليل البيانات: ${error.message}`);
        }
    }

    isHeaderRow(row) {
        // فحص إذا كان الصف يحتوي على كلمات العناوين
        const rowText = row.map(cell => String(cell || '').toLowerCase()).join(' ');

        const headerKeywords = [
            'اسم الصنف', 'name', 'item name',
            'الكود', 'code', 'item code',
            'المواصفات', 'specs', 'specification',
            'رقم الصنف', 'item number',
            'الرصيد', 'stock', 'quantity',
            'رقم الشلف', 'shelf number',
            'رقم العين', 'eye number',
            'اسم المورد', 'supplier name',
            'تاريخ التوريد', 'supply date'
        ];

        // إذا وجدت 2 أو أكثر من كلمات العناوين المحددة
        const foundKeywords = headerKeywords.filter(keyword =>
            rowText.includes(keyword)
        ).length;

        return foundKeywords >= 2;
    }

    cleanString(value) {
        if (value === null || value === undefined) return '';
        return String(value).trim();
    }

    parseNumber(value) {
        if (value === null || value === undefined || value === '') return 0;
        const num = parseFloat(value);
        return isNaN(num) ? 0 : num;
    }

    updateCurrentDate() {
        const now = new Date();
        const dateString = now.toLocaleDateString('ar-EG', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
        document.getElementById('currentDate').textContent = dateString;
    }

    updateCurrentTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('ar-EG', {
            hour: 'numeric',
            minute: '2-digit',
            second: '2-digit',
            hour12: true
        });
        document.getElementById('currentTime').textContent = timeString;
    }

    startTimeUpdater() {
        // تحديث الوقت كل ثانية
        this.timeInterval = setInterval(() => {
            this.updateCurrentTime();
        }, 1000);
    }

    showContextMenu(event, itemId) {
        event.preventDefault();
        this.selectedItemId = itemId;

        const contextMenu = document.getElementById('contextMenu');
        const x = event.pageX;
        const y = event.pageY;

        // التأكد من أن القائمة لا تخرج من حدود الشاشة
        const menuWidth = 150;
        const menuHeight = 80;
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;

        let finalX = x;
        let finalY = y;

        if (x + menuWidth > windowWidth) {
            finalX = x - menuWidth;
        }

        if (y + menuHeight > windowHeight) {
            finalY = y - menuHeight;
        }

        contextMenu.style.left = finalX + 'px';
        contextMenu.style.top = finalY + 'px';
        contextMenu.classList.remove('hidden');
    }

    hideContextMenu() {
        const contextMenu = document.getElementById('contextMenu');
        contextMenu.classList.add('hidden');
        this.selectedItemId = null;
    }

    formatDate(dateString) {
        if (!dateString) return '';
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-EG');
        } catch {
            return dateString;
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showNotification(message, type = 'info', duration = 5000) {
        // الحصول على حاوي الإشعارات
        let container = document.getElementById('notificationContainer');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notificationContainer';
            document.body.appendChild(container);
        }

        // إنشاء عنصر الإشعار
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        // إنشاء معرف فريد للإشعار
        const notificationId = Date.now() + Math.random();
        notification.setAttribute('data-id', notificationId);

        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)} notification-icon"></i>
                <span class="notification-message">${message}</span>
                <button class="notification-close" onclick="app.closeNotification('${notificationId}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="progress-bar"></div>
        `;

        // إضافة الإشعار إلى الحاوي
        container.appendChild(notification);

        // تشغيل صوت الإشعار (اختياري)
        this.playNotificationSound(type);

        // إزالة الإشعار تلقائياً بعد المدة المحددة
        setTimeout(() => {
            this.closeNotification(notificationId);
        }, duration);

        // إزالة الإشعارات القديمة إذا كان هناك أكثر من 5
        this.cleanupOldNotifications();
    }

    closeNotification(notificationId) {
        const notification = document.querySelector(`[data-id="${notificationId}"]`);
        if (notification) {
            notification.classList.add('removing');
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }
    }

    cleanupOldNotifications() {
        const container = document.getElementById('notificationContainer');
        if (container) {
            const notifications = container.querySelectorAll('.notification');
            if (notifications.length > 5) {
                // إزالة الإشعارات الأقدم
                for (let i = 0; i < notifications.length - 5; i++) {
                    const oldNotification = notifications[i];
                    if (!oldNotification.classList.contains('removing')) {
                        oldNotification.classList.add('removing');
                        setTimeout(() => {
                            if (oldNotification.parentNode) {
                                oldNotification.parentNode.removeChild(oldNotification);
                            }
                        }, 300);
                    }
                }
            }
        }
    }

    playNotificationSound(type) {
        // يمكن إضافة أصوات مختلفة لكل نوع إشعار
        try {
            const audio = new Audio();
            switch (type) {
                case 'success':
                    // صوت نجاح
                    break;
                case 'error':
                    // صوت خطأ
                    break;
                case 'warning':
                    // صوت تحذير
                    break;
                default:
                    // صوت معلومات
                    break;
            }
            // audio.play(); // يمكن تفعيله لاحقاً
        } catch (error) {
            // تجاهل أخطاء الصوت
        }
    }

    getNotificationIcon(type) {
        switch (type) {
            case 'success': return 'check-circle';
            case 'error': return 'exclamation-circle';
            case 'warning': return 'exclamation-triangle';
            default: return 'info-circle';
        }
    }

    // دوال مساعدة للإشعارات المختلفة
    showSuccessNotification(message, duration = 4000) {
        this.showNotification(message, 'success', duration);
    }

    showErrorNotification(message, duration = 6000) {
        this.showNotification(message, 'error', duration);
    }

    showWarningNotification(message, duration = 5000) {
        this.showNotification(message, 'warning', duration);
    }

    showInfoNotification(message, duration = 4000) {
        this.showNotification(message, 'info', duration);
    }

    // إشعار مع إجراء
    showActionNotification(message, actionText, actionCallback, type = 'info') {
        const container = document.getElementById('notificationContainer') || this.createNotificationContainer();

        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        const notificationId = Date.now() + Math.random();
        notification.setAttribute('data-id', notificationId);

        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${this.getNotificationIcon(type)} notification-icon"></i>
                <span class="notification-message">${message}</span>
                <button class="notification-action" onclick="${actionCallback}">
                    ${actionText}
                </button>
                <button class="notification-close" onclick="app.closeNotification('${notificationId}')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        container.appendChild(notification);

        // إزالة تلقائية بعد 10 ثوان للإشعارات التي تحتوي على إجراءات
        setTimeout(() => {
            this.closeNotification(notificationId);
        }, 10000);
    }

    createNotificationContainer() {
        const container = document.createElement('div');
        container.id = 'notificationContainer';
        document.body.appendChild(container);
        return container;
    }

    // مسح جميع الإشعارات
    clearAllNotifications() {
        const container = document.getElementById('notificationContainer');
        if (container) {
            const notifications = container.querySelectorAll('.notification');
            notifications.forEach(notification => {
                notification.classList.add('removing');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            });
        }
    }

    // دوال البحث المتقدم
    async openAdvancedSearch() {
        try {
            // إظهار النافذة فوراً لتجنب اللاج
            const modal = document.getElementById('advancedSearchModal');
            modal.classList.remove('hidden');

            // تحميل قائمة الموردين في الخلفية
            setTimeout(async () => {
                await this.loadSuppliersForSearch();
            }, 100);

            // التركيز على الحقل الأول
            setTimeout(() => {
                const searchNameField = document.getElementById('searchName');
                if (searchNameField) {
                    searchNameField.focus();
                }
            }, 150);
        } catch (error) {
            console.error('خطأ في فتح البحث المتقدم:', error);
            this.showErrorNotification('خطأ في فتح نافذة البحث');
        }
    }

    closeAdvancedSearch() {
        document.getElementById('advancedSearchModal').classList.add('hidden');
        document.getElementById('searchResults').classList.add('hidden');
    }

    async loadSuppliersForSearch() {
        try {
            // التحقق من وجود العنصر أولاً
            const supplierSelect = document.getElementById('searchSupplier');
            if (!supplierSelect) return;

            // إظهار حالة التحميل
            supplierSelect.innerHTML = '<option value="">جاري التحميل...</option>';

            const items = await dbManager.getAllItems();
            const suppliers = [...new Set(items.map(item => item.supplierName).filter(Boolean))];

            // مسح الخيارات وإضافة الخيار الافتراضي
            supplierSelect.innerHTML = '<option value="">جميع الموردين</option>';

            // إضافة الموردين بشكل تدريجي لتجنب اللاج
            suppliers.forEach((supplier, index) => {
                setTimeout(() => {
                    const option = document.createElement('option');
                    option.value = supplier;
                    option.textContent = supplier;
                    supplierSelect.appendChild(option);
                }, index * 10); // تأخير 10ms بين كل مورد
            });
        } catch (error) {
            console.error('خطأ في تحميل الموردين:', error);
            const supplierSelect = document.getElementById('searchSupplier');
            if (supplierSelect) {
                supplierSelect.innerHTML = '<option value="">خطأ في التحميل</option>';
            }
        }
    }

    clearAdvancedSearchForm() {
        document.getElementById('advancedSearchForm').reset();
        document.getElementById('searchResults').classList.add('hidden');
    }

    async handleAdvancedSearch(e) {
        e.preventDefault();

        try {
            // إظهار حالة التحميل
            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري البحث...';
            submitBtn.disabled = true;

            // الحصول على معايير البحث
            const searchCriteria = this.getAdvancedSearchCriteria();

            // التحقق من وجود معايير بحث
            if (!searchCriteria.name && !searchCriteria.code && !searchCriteria.itemCode &&
                !searchCriteria.supplier && !searchCriteria.shelf && !searchCriteria.eye &&
                !searchCriteria.stockMin && !searchCriteria.stockMax && !searchCriteria.dateFrom &&
                !searchCriteria.dateTo && !searchCriteria.stockStatus && !searchCriteria.specs &&
                !searchCriteria.dispensing) {

                this.showWarningNotification('يرجى إدخال معايير البحث');
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                return;
            }

            // تنفيذ البحث
            const results = await this.performAdvancedSearch(searchCriteria);

            // عرض النتائج
            setTimeout(() => {
                this.displayAdvancedSearchResults(results);
                this.displayItems(results);

                // إظهار زر إعادة التعيين
                document.getElementById('resetSearchBtn').classList.remove('hidden');

                // إغلاق نافذة البحث المتقدم
                this.closeAdvancedSearch();

                this.showSuccessNotification(`تم العثور على ${results.length} نتيجة`);

                // استعادة الزر
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 100);

        } catch (error) {
            console.error('خطأ في البحث المتقدم:', error);
            this.showErrorNotification('خطأ في البحث المتقدم');

            // استعادة الزر في حالة الخطأ
            const submitBtn = e.target.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-search ml-2"></i>بحث';
            submitBtn.disabled = false;
        }
    }

    getAdvancedSearchCriteria() {
        return {
            name: document.getElementById('searchName').value.trim(),
            code: document.getElementById('searchCode').value.trim(),
            itemCode: document.getElementById('searchItemCode').value.trim(),
            supplier: document.getElementById('searchSupplier').value,
            shelf: document.getElementById('searchShelf').value.trim(),
            eye: document.getElementById('searchEye').value.trim(),
            stockMin: document.getElementById('searchStockMin').value,
            stockMax: document.getElementById('searchStockMax').value,
            dateFrom: document.getElementById('searchDateFrom').value,
            dateTo: document.getElementById('searchDateTo').value,
            stockStatus: document.getElementById('searchStockStatus').value,
            specs: document.getElementById('searchSpecs').value.trim(),
            dispensing: document.getElementById('searchDispensing').value.trim()
        };
    }

    async performAdvancedSearch(criteria) {
        const allItems = await dbManager.getAllItems();

        // تحسين الأداء للبيانات الكبيرة
        if (allItems.length > 1000) {
            // معالجة البيانات على دفعات لتجنب اللاج
            const batchSize = 100;
            const results = [];

            for (let i = 0; i < allItems.length; i += batchSize) {
                const batch = allItems.slice(i, i + batchSize);
                const batchResults = batch.filter(item => this.matchesCriteria(item, criteria));
                results.push(...batchResults);

                // إعطاء فرصة للمتصفح للتنفس
                if (i % 500 === 0) {
                    await new Promise(resolve => setTimeout(resolve, 1));
                }
            }

            return results;
        }

        return allItems.filter(item => this.matchesCriteria(item, criteria));
    }

    matchesCriteria(item, criteria) {
        // البحث في اسم الصنف
        if (criteria.name && !item.name?.toLowerCase().includes(criteria.name.toLowerCase())) {
            return false;
        }

        // البحث في الكود
        if (criteria.code && !item.itemCode?.toLowerCase().includes(criteria.code.toLowerCase())) {
            return false;
        }

        // البحث في رقم الصنف المخزني
        if (criteria.itemCode && !item.code?.toLowerCase().includes(criteria.itemCode.toLowerCase())) {
            return false;
        }

        // البحث في المورد
        if (criteria.supplier && item.supplierName !== criteria.supplier) {
            return false;
        }

        // البحث في رقم الشلف
        if (criteria.shelf && !item.shelfNumber?.toLowerCase().includes(criteria.shelf.toLowerCase())) {
            return false;
        }

        // البحث في رقم العين
        if (criteria.eye && !item.eyeNumber?.toLowerCase().includes(criteria.eye.toLowerCase())) {
            return false;
        }

        // فلتر الرصيد
        const stock = parseInt(item.stock) || 0;
        if (criteria.stockMin && stock < parseInt(criteria.stockMin)) {
            return false;
        }
        if (criteria.stockMax && stock > parseInt(criteria.stockMax)) {
            return false;
        }

        // فلتر حالة المخزون
        if (criteria.stockStatus) {
            switch (criteria.stockStatus) {
                case 'available':
                    if (stock <= 10) return false;
                    break;
                case 'low':
                    if (stock === 0 || stock > 10) return false;
                    break;
                case 'empty':
                    if (stock !== 0) return false;
                    break;
            }
        }

        // فلتر التاريخ
        if (criteria.dateFrom || criteria.dateTo) {
            const itemDate = new Date(item.supplyDate);
            if (criteria.dateFrom && itemDate < new Date(criteria.dateFrom)) {
                return false;
            }
            if (criteria.dateTo && itemDate > new Date(criteria.dateTo)) {
                return false;
            }
        }

        // البحث في المواصفات
        if (criteria.specs && !item.specs?.toLowerCase().includes(criteria.specs.toLowerCase())) {
            return false;
        }

        // البحث في بيان الصرف
        if (criteria.dispensing && !item.dispensingInfo?.toLowerCase().includes(criteria.dispensing.toLowerCase())) {
            return false;
        }

        return true;
    }

    displayAdvancedSearchResults(results) {
        const resultsContainer = document.getElementById('searchResults');
        const resultsList = document.getElementById('searchResultsList');
        const resultsCount = document.getElementById('searchResultsCount');

        if (!resultsContainer || !resultsList || !resultsCount) return;

        resultsCount.textContent = `${results.length} نتيجة`;

        if (results.length === 0) {
            resultsList.innerHTML = '<p class="text-gray-500 text-center py-4">لم يتم العثور على نتائج</p>';
        } else {
            // عرض أول 5 نتائج فقط لتجنب اللاج
            const displayResults = results.slice(0, 5);

            // إنشاء HTML بطريقة محسنة
            const fragment = document.createDocumentFragment();

            displayResults.forEach(item => {
                const div = document.createElement('div');
                div.className = 'flex items-center justify-between py-2 border-b border-gray-200 last:border-b-0';
                div.innerHTML = `
                    <div>
                        <div class="font-medium text-gray-900">${this.escapeHtml(item.name || '')}</div>
                        <div class="text-sm text-gray-500">${this.escapeHtml(item.itemCode || '')} - ${this.escapeHtml(item.code || '')}</div>
                    </div>
                    <div class="text-sm text-gray-600">
                        رصيد: ${item.stock || 0}
                    </div>
                `;
                fragment.appendChild(div);
            });

            // مسح المحتوى السابق وإضافة الجديد
            resultsList.innerHTML = '';
            resultsList.appendChild(fragment);

            if (results.length > 5) {
                const moreInfo = document.createElement('p');
                moreInfo.className = 'text-sm text-gray-500 text-center py-2';
                moreInfo.textContent = `وعرض ${results.length - 5} نتيجة أخرى في الجدول الرئيسي`;
                resultsList.appendChild(moreInfo);
            }
        }

        resultsContainer.classList.remove('hidden');
    }

    // إضافة event listeners لأزرار الحذف
    setupDeleteButtons() {
        // إزالة event listeners السابقة
        if (this.handleDeleteButtonClick) {
            document.removeEventListener('click', this.handleDeleteButtonClick);
        }

        // إضافة event listener جديد
        this.handleDeleteButtonClick = (e) => {
            if (e.target.closest('.delete-update-btn')) {
                e.preventDefault();
                e.stopPropagation();

                const button = e.target.closest('.delete-update-btn');
                const updateId = button.getAttribute('data-update-id');

                console.log('تم النقر على زر الحذف، معرف التحديث:', updateId);

                if (updateId) {
                    this.deleteDispensingUpdate(updateId);
                } else {
                    console.error('لم يتم العثور على معرف التحديث');
                }
            }
        };

        document.addEventListener('click', this.handleDeleteButtonClick);
        console.log('تم إعداد event listeners لأزرار الحذف');
    }

    // إعداد أزرار الحذف مباشرة
    setupDeleteButtonsDirectly() {
        const deleteButtons = document.querySelectorAll('.delete-update-btn');
        console.log(`تم العثور على ${deleteButtons.length} زر حذف`);

        deleteButtons.forEach((button, index) => {
            const updateId = button.getAttribute('data-update-id');
            console.log(`زر ${index + 1}: معرف التحديث = ${updateId}`);

            // إزالة event listeners السابقة
            button.removeEventListener('click', button._deleteHandler);

            // إضافة event listener جديد
            button._deleteHandler = (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('تم النقر على زر الحذف، معرف التحديث:', updateId);
                this.deleteDispensingUpdate(updateId);
            };

            button.addEventListener('click', button._deleteHandler);
        });
    }

    // إعادة تعيين البحث
    async resetSearch() {
        try {
            // مسح حقل البحث العادي
            document.getElementById('searchInput').value = '';

            // إخفاء زر إعادة التعيين
            document.getElementById('resetSearchBtn').classList.add('hidden');

            // إعادة تحميل جميع البيانات
            await this.loadData();

            this.showInfoNotification('تم إعادة تعيين البحث');
        } catch (error) {
            console.error('خطأ في إعادة تعيين البحث:', error);
            this.showErrorNotification('خطأ في إعادة تعيين البحث');
        }
    }

    // دوال إدارة بيان الصرف
    async openDispensingModal(itemId) {
        try {
            // تحويل المعرف إلى رقم إذا كان نص
            const numericId = typeof itemId === 'string' ? parseInt(itemId) : itemId;
            this.currentDispensingItemId = numericId;

            console.log('فتح نافذة بيان الصرف للصنف:', numericId);

            // التحقق من وجود قاعدة البيانات
            if (!dbManager.db) {
                console.log('قاعدة البيانات غير متاحة، محاولة إعادة الاتصال...');
                await dbManager.init();
            }

            // الحصول على بيانات الصنف
            const item = await dbManager.getItem(numericId);
            console.log('بيانات الصنف المسترجعة:', item);

            if (!item) {
                // محاولة البحث في جميع الأصناف كحل بديل
                console.log('محاولة البحث في جميع الأصناف...');
                const allItems = await dbManager.getAllItems();
                console.log('عدد الأصناف الموجودة:', allItems.length);
                console.log('معرفات الأصناف الموجودة:', allItems.map(i => `${i.id} (${typeof i.id})`));

                const foundItem = allItems.find(i => i.id == numericId);
                if (foundItem) {
                    console.log('تم العثور على الصنف في القائمة الكاملة:', foundItem);
                    this.displayDispensingModal(foundItem);
                    return;
                }

                this.showErrorNotification(`الصنف غير موجود (المعرف: ${numericId})`);
                return;
            }

            this.displayDispensingModal(item);

        } catch (error) {
            console.error('خطأ في فتح نافذة بيان الصرف:', error);
            this.showErrorNotification('خطأ في فتح نافذة بيان الصرف');
        }
    }

    async displayDispensingModal(item) {
        try {
            // تحديث عنوان النافذة
            document.getElementById('dispensingItemName').textContent = item.name || 'غير محدد';

            // تحميل تحديثات بيان الصرف
            await this.loadDispensingUpdates(item.id);

            // تعيين التاريخ الحالي
            const now = new Date();
            const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
            document.getElementById('dispensingDate').value = localDateTime;

            // إظهار النافذة
            document.getElementById('dispensingModal').classList.remove('hidden');
            document.getElementById('dispensingType').focus();

        } catch (error) {
            console.error('خطأ في عرض نافذة بيان الصرف:', error);
            this.showErrorNotification('خطأ في عرض نافذة بيان الصرف');
        }
    }

    closeDispensingModal() {
        document.getElementById('dispensingModal').classList.add('hidden');
        this.clearDispensingForm();
        this.currentDispensingItemId = null;
    }

    clearDispensingForm() {
        document.getElementById('dispensingUpdateForm').reset();

        // تعيين التاريخ الحالي مرة أخرى
        const now = new Date();
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
        document.getElementById('dispensingDate').value = localDateTime;
    }

    async loadDispensingUpdates(itemId) {
        try {
            const updates = await dbManager.getDispensingUpdates(itemId);
            const updatesList = document.getElementById('dispensingUpdatesList');
            const updatesCount = document.getElementById('dispensingUpdatesCount');
            const noUpdatesMessage = document.getElementById('noDispensingUpdates');

            updatesCount.textContent = `${updates.length} تحديث`;

            if (updates.length === 0) {
                updatesList.innerHTML = '';
                noUpdatesMessage.classList.remove('hidden');
            } else {
                noUpdatesMessage.classList.add('hidden');

                // طباعة معرفات التحديثات للتشخيص
                console.log('التحديثات المحملة:', updates.map(u => ({ id: u.id, type: typeof u.id })));

                updatesList.innerHTML = updates.map(update => this.createDispensingUpdateCard(update)).join('');

                // إعداد أزرار الحذف بعد إضافة المحتوى
                this.setupDeleteButtonsDirectly();
            }

        } catch (error) {
            console.error('خطأ في تحميل تحديثات بيان الصرف:', error);
            this.showErrorNotification('خطأ في تحميل تحديثات بيان الصرف');
        }
    }

    createDispensingUpdateCard(update) {
        const date = new Date(update.date);
        const formattedDate = date.toLocaleDateString('ar-EG', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        const typeColors = {
            'صرف': 'bg-red-100 text-red-800 border-red-200',
            'إضافة': 'bg-green-100 text-green-800 border-green-200',
            'جرد': 'bg-purple-100 text-purple-800 border-purple-200'
        };

        const typeClass = typeColors[update.type] || 'bg-gray-100 text-gray-800 border-gray-200';

        // طباعة معرف التحديث للتشخيص
        console.log('إنشاء كارت للتحديث:', { id: update.id, type: typeof update.id });

        return `
            <div class="dispensing-update-card bg-white border border-gray-200 rounded-lg p-4">
                <div class="flex items-center justify-between mb-3">
                    <span class="operation-type-badge ${typeClass}">
                        ${update.type}
                    </span>
                    <span class="text-sm text-gray-500">${formattedDate}</span>
                </div>

                <div class="grid grid-cols-2 gap-4 mb-3">
                    <div>
                        <span class="text-sm font-medium text-gray-700">الكمية:</span>
                        <span class="text-sm text-gray-900 mr-2">${update.quantity}</span>
                    </div>
                    <div>
                        <span class="text-sm font-medium text-gray-700">المسؤول:</span>
                        <span class="text-sm text-gray-900 mr-2">${this.escapeHtml(update.responsible || '')}</span>
                    </div>
                </div>

                ${update.notes ? `
                    <div class="mt-3 p-3 bg-gray-50 rounded-lg">
                        <span class="text-sm font-medium text-gray-700">ملاحظات:</span>
                        <p class="text-sm text-gray-600 mt-1">${this.escapeHtml(update.notes)}</p>
                    </div>
                ` : ''}

                <div class="flex justify-end mt-3">
                    <button
                        data-update-id="${update.id}"
                        class="delete-update-btn text-red-600 hover:text-red-800 text-sm"
                        title="حذف التحديث (ID: ${update.id})"
                    >
                        <i class="fas fa-trash ml-1"></i>
                        حذف
                    </button>
                </div>
            </div>
        `;
    }

    async handleDispensingUpdate(e) {
        e.preventDefault();

        try {
            const updateData = {
                itemId: this.currentDispensingItemId,
                type: document.getElementById('dispensingType').value,
                quantity: parseFloat(document.getElementById('dispensingQuantity').value) || 0,
                responsible: document.getElementById('dispensingResponsible').value.trim(),
                date: document.getElementById('dispensingDate').value,
                notes: document.getElementById('dispensingNotes').value.trim()
            };

            // التحقق من البيانات المطلوبة
            if (!updateData.quantity || !updateData.responsible || !updateData.date) {
                this.showWarningNotification('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            // حفظ التحديث
            await dbManager.addDispensingUpdate(updateData);

            // تحديث الرصيد إذا كانت العملية صرف أو إضافة
            if (updateData.type === 'صرف' || updateData.type === 'إضافة') {
                await this.updateItemStock(updateData.itemId, updateData.type, updateData.quantity);
            }

            // إعادة تحميل التحديثات
            await this.loadDispensingUpdates(this.currentDispensingItemId);

            // مسح النموذج
            this.clearDispensingForm();

            // إعادة تحميل البيانات الرئيسية
            await this.loadData();

            this.showSuccessNotification('تم إضافة التحديث بنجاح');

        } catch (error) {
            console.error('خطأ في حفظ تحديث بيان الصرف:', error);
            this.showErrorNotification('خطأ في حفظ التحديث');
        }
    }

    async updateItemStock(itemId, type, quantity) {
        try {
            const item = await dbManager.getItem(itemId);
            if (!item) return;

            let newStock = parseInt(item.stock) || 0;

            if (type === 'صرف') {
                newStock = Math.max(0, newStock - quantity);
            } else if (type === 'إضافة') {
                newStock += quantity;
            }

            await dbManager.updateItem(itemId, { ...item, stock: newStock });

        } catch (error) {
            console.error('خطأ في تحديث الرصيد:', error);
        }
    }

    async deleteDispensingUpdate(updateId) {
        try {
            console.log('محاولة حذف التحديث:', updateId, 'نوع البيانات:', typeof updateId);

            if (!updateId) {
                console.error('معرف التحديث فارغ');
                this.showErrorNotification('خطأ: معرف التحديث غير صحيح');
                return;
            }

            const confirmed = confirm('هل أنت متأكد من حذف هذا التحديث؟');
            if (!confirmed) {
                console.log('تم إلغاء الحذف من قبل المستخدم');
                return;
            }

            console.log('بدء عملية الحذف...');

            // التحقق من وجود قاعدة البيانات
            if (!dbManager.db) {
                console.error('قاعدة البيانات غير متاحة');
                this.showErrorNotification('قاعدة البيانات غير متاحة');
                return;
            }

            await dbManager.deleteDispensingUpdate(updateId);

            console.log('تم حذف التحديث، إعادة تحميل القائمة...');
            await this.loadDispensingUpdates(this.currentDispensingItemId);

            this.showSuccessNotification('تم حذف التحديث بنجاح');

        } catch (error) {
            console.error('خطأ في حذف التحديث:', error);
            this.showErrorNotification('خطأ في حذف التحديث: ' + error.message);
        }
    }
}

// تشغيل التطبيق عند تحميل الصفحة
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new WarehouseApp();
});
