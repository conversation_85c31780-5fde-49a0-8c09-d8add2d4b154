// نظام النسخ الاحتياطي والاستيراد/التصدير
class BackupManager {
    constructor() {
        this.dbManager = null;
    }

    setDatabaseManager(dbManager) {
        this.dbManager = dbManager;
    }

    // إنشاء نسخة احتياطية
    async createBackup() {
        try {
            if (!this.dbManager) {
                throw new Error('مدير قاعدة البيانات غير متاح');
            }

            // الحصول على جميع البيانات
            const items = await this.dbManager.getAllItems();

            // إنشاء كائن النسخة الاحتياطية
            const backup = {
                version: '1.0',
                timestamp: new Date().toISOString(),
                appName: 'كيما - إدارة المخازن',
                itemsCount: items.length,
                data: {
                    items: items
                }
            };

            // تحويل البيانات إلى JSON
            const backupData = JSON.stringify(backup, null, 2);

            // إنشاء اسم الملف
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
            const filename = `كيما-نسخة-احتياطية-${timestamp}.json`;

            // تحميل الملف
            this.downloadFile(backupData, filename, 'application/json');

            return {
                success: true,
                filename: filename,
                itemsCount: items.length
            };
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            throw error;
        }
    }

    // استعادة النسخة الاحتياطية
    async restoreBackup(file) {
        try {
            const content = await this.readFileContent(file);
            const backup = JSON.parse(content);

            // التحقق من صحة النسخة الاحتياطية
            if (!backup.data || !backup.data.items) {
                throw new Error('ملف النسخة الاحتياطية غير صحيح');
            }

            // مسح البيانات الحالية (بعد تأكيد المستخدم)
            const confirmed = await this.showConfirmDialog(
                'استعادة النسخة الاحتياطية',
                'سيتم حذف جميع البيانات الحالية واستبدالها بالنسخة الاحتياطية. هل أنت متأكد؟'
            );

            if (!confirmed) {
                return { success: false, message: 'تم إلغاء العملية' };
            }

            // مسح البيانات الحالية
            await this.dbManager.clearAllData();

            // استيراد البيانات من النسخة الاحتياطية
            let successCount = 0;
            let errorCount = 0;

            for (const item of backup.data.items) {
                try {
                    // إزالة المعرف لإنشاء معرف جديد
                    const { id, ...itemData } = item;
                    await this.dbManager.addItem(itemData);
                    successCount++;
                } catch (error) {
                    console.error('خطأ في استيراد الصنف:', error);
                    errorCount++;
                }
            }

            return {
                success: true,
                itemsRestored: successCount,
                errors: errorCount,
                backupDate: backup.timestamp
            };
        } catch (error) {
            console.error('خطأ في استعادة النسخة الاحتياطية:', error);
            throw error;
        }
    }

    // تصدير إلى Excel
    async exportToExcel() {
        try {
            if (!this.dbManager) {
                throw new Error('مدير قاعدة البيانات غير متاح');
            }

            // الحصول على البيانات المنسقة للتصدير
            const exportData = await this.dbManager.exportData();

            if (exportData.length === 0) {
                throw new Error('لا توجد بيانات للتصدير');
            }

            // إنشاء ورقة عمل Excel
            const worksheet = XLSX.utils.json_to_sheet(exportData);

            // تحسين عرض الأعمدة
            const columnWidths = [
                { wch: 25 }, // اسم الصنف
                { wch: 30 }, // المواصفات
                { wch: 15 }, // رقم الصنف المخزني
                { wch: 10 }, // الرصيد
                { wch: 12 }, // رقم الشلف
                { wch: 12 }, // رقم العين
                { wch: 25 }, // بيان الصرف
                { wch: 20 }, // اسم المورد
                { wch: 15 }, // تاريخ التوريد
                { wch: 15 }, // تاريخ الإنشاء
                { wch: 15 }  // تاريخ التحديث
            ];
            worksheet['!cols'] = columnWidths;

            // إنشاء كتاب العمل
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'أصناف المخزن');

            // إضافة معلومات إضافية
            const infoSheet = XLSX.utils.aoa_to_sheet([
                ['تقرير مخازن شركة الصناعات الكيماوية المصرية - كيما'],
                ['تاريخ التصدير:', new Date().toLocaleDateString('ar-EG')],
                ['عدد الأصناف:', exportData.length],
                [''],
                ['ملاحظات:'],
                ['- هذا التقرير تم إنشاؤه تلقائياً من نظام إدارة المخازن'],
                ['- للاستفسارات يرجى التواصل مع إدارة المخازن']
            ]);
            XLSX.utils.book_append_sheet(workbook, infoSheet, 'معلومات التقرير');

            // تحويل إلى buffer
            const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

            // إنشاء اسم الملف
            const timestamp = new Date().toISOString().split('T')[0];
            const filename = `كيما-المخازن-${timestamp}.xlsx`;

            // تحميل الملف
            this.downloadFile(excelBuffer, filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

            return {
                success: true,
                filename: filename,
                itemsCount: exportData.length
            };
        } catch (error) {
            console.error('خطأ في تصدير Excel:', error);
            throw error;
        }
    }

    // استيراد من Excel
    async importFromExcel(file) {
        try {
            const buffer = await this.readFileAsArrayBuffer(file);
            const workbook = XLSX.read(buffer, { type: 'array' });

            // الحصول على أول ورقة عمل
            const sheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[sheetName];

            // تحويل البيانات إلى JSON
            const jsonData = XLSX.utils.sheet_to_json(worksheet);

            if (jsonData.length === 0) {
                throw new Error('الملف فارغ أو لا يحتوي على بيانات صحيحة');
            }

            // استيراد البيانات
            const result = await this.dbManager.importData(jsonData);

            return result;
        } catch (error) {
            console.error('خطأ في استيراد Excel:', error);
            throw error;
        }
    }

    // قراءة محتوى الملف كنص
    readFileContent(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(e.target.error);
            reader.readAsText(file, 'UTF-8');
        });
    }

    // قراءة محتوى الملف كـ ArrayBuffer
    readFileAsArrayBuffer(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => resolve(e.target.result);
            reader.onerror = (e) => reject(e.target.error);
            reader.readAsArrayBuffer(file);
        });
    }

    // تحميل ملف
    downloadFile(data, filename, mimeType) {
        const blob = new Blob([data], { type: mimeType });
        const url = URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // تنظيف الذاكرة
        setTimeout(() => URL.revokeObjectURL(url), 100);
    }

    // إظهار نافذة تأكيد
    async showConfirmDialog(title, message) {
        if (window.electronAPI && window.electronAPI.showMessage) {
            const result = await window.electronAPI.showMessage({
                type: 'question',
                buttons: ['نعم', 'لا'],
                defaultId: 1,
                title: title,
                message: message
            });
            return result.response === 0;
        } else {
            return confirm(message);
        }
    }

    // إظهار رسالة معلومات
    async showInfoDialog(title, message) {
        if (window.electronAPI && window.electronAPI.showMessage) {
            await window.electronAPI.showMessage({
                type: 'info',
                buttons: ['موافق'],
                title: title,
                message: message
            });
        } else {
            alert(message);
        }
    }

    // إظهار رسالة خطأ
    async showErrorDialog(title, message) {
        if (window.electronAPI && window.electronAPI.showMessage) {
            await window.electronAPI.showMessage({
                type: 'error',
                buttons: ['موافق'],
                title: title,
                message: message
            });
        } else {
            alert(message);
        }
    }
}

// إنشاء مثيل من مدير النسخ الاحتياطي
const backupManager = new BackupManager();

// تحميل مكتبة XLSX إذا لم تكن متاحة
if (typeof XLSX === 'undefined') {
    const script = document.createElement('script');
    script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
    document.head.appendChild(script);
}
