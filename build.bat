@echo off
echo ========================================
echo    تصدير تطبيق كيما - إدارة المخازن
echo ========================================
echo.

echo [1/5] التحقق من Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Node.js غير مثبت
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)
echo ✓ Node.js مثبت

echo.
echo [2/5] تثبيت التبعيات...
call npm install
if errorlevel 1 (
    echo خطأ في تثبيت التبعيات
    pause
    exit /b 1
)
echo ✓ تم تثبيت التبعيات

echo.
echo [3/5] تثبيت electron-builder...
call npm install -g electron-builder
if errorlevel 1 (
    echo تحذير: فشل في تثبيت electron-builder عالمياً
    echo سيتم المحاولة محلياً...
    call npm install electron-builder --save-dev
)
echo ✓ electron-builder جاهز

echo.
echo [4/5] بناء التطبيق...
call electron-builder --win
if errorlevel 1 (
    echo خطأ في بناء التطبيق
    echo يرجى مراجعة ملف BUILD-INSTRUCTIONS.md للحلول
    pause
    exit /b 1
)
echo ✓ تم بناء التطبيق بنجاح

echo.
echo [5/5] التحقق من الملفات الناتجة...
if exist "dist\win-unpacked\kima-warehouse-app.exe" (
    echo ✓ النسخة المحمولة: dist\win-unpacked\
) else (
    echo تحذير: النسخة المحمولة غير موجودة
)

if exist "dist\kima-warehouse-app Setup*.exe" (
    echo ✓ ملف التثبيت: dist\
) else (
    echo تحذير: ملف التثبيت غير موجود
)

echo.
echo ========================================
echo           تم التصدير بنجاح!
echo ========================================
echo.
echo الملفات الناتجة في مجلد: dist\
echo - النسخة المحمولة: dist\win-unpacked\
echo - ملف التثبيت: dist\kima-warehouse-app Setup 1.0.0.exe
echo.
echo لتشغيل التطبيق:
echo cd dist\win-unpacked
echo kima-warehouse-app.exe
echo.
pause
