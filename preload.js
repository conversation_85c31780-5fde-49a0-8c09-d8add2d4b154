const { contextBridge, ipcRenderer } = require('electron');

// تعريض APIs آمنة للواجهة
contextBridge.exposeInMainWorld('electronAPI', {
  // معالجة الملفات والمجلدات
  selectBackupFolder: () => ipcRenderer.invoke('select-backup-folder'),
  selectExcelFile: () => ipcRenderer.invoke('select-excel-file'),
  saveExcelFile: () => ipcRenderer.invoke('save-excel-file'),
  
  // معالجة الرسائل
  showMessage: (options) => ipcRenderer.invoke('show-message', options),
  
  // معلومات النظام
  platform: process.platform,
  
  // إشعارات
  showNotification: (title, body) => {
    new Notification(title, { body });
  }
});
