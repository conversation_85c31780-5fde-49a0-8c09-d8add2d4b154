const { app, BrowserWindow, Menu, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');

// تعطيل قائمة التطبيق الافتراضية
Menu.setApplicationMenu(null);

let mainWindow;

function createWindow() {
  // إنشاء النافذة الرئيسية
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700,
    icon: path.join(__dirname, 'assets', 'icon.png'),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    show: false,
    titleBarStyle: 'default',
    frame: true
  });

  // تحميل صفحة تسجيل الدخول أولاً
  mainWindow.loadFile('src/login.html');

  // إظهار النافذة عند الانتهاء من التحميل
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();

    // فتح أدوات المطور في وضع التطوير
    if (process.argv.includes('--dev')) {
      mainWindow.webContents.openDevTools();
    }
  });

  // إغلاق التطبيق عند إغلاق النافذة الرئيسية
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// تشغيل التطبيق
app.whenReady().then(() => {
  createWindow();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// إغلاق التطبيق على جميع المنصات عدا macOS
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// معالجة اختيار مجلد النسخ الاحتياطي
ipcMain.handle('select-backup-folder', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openDirectory'],
    title: 'اختر مجلد النسخ الاحتياطي'
  });

  return result.canceled ? null : result.filePaths[0];
});

// معالجة اختيار ملف Excel للاستيراد
ipcMain.handle('select-excel-file', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openFile'],
    filters: [
      { name: 'Excel Files', extensions: ['xlsx', 'xls'] }
    ],
    title: 'اختر ملف Excel للاستيراد'
  });

  return result.canceled ? null : result.filePaths[0];
});

// معالجة حفظ ملف Excel
ipcMain.handle('save-excel-file', async () => {
  const result = await dialog.showSaveDialog(mainWindow, {
    filters: [
      { name: 'Excel Files', extensions: ['xlsx'] }
    ],
    defaultPath: 'كيما-المخازن.xlsx',
    title: 'حفظ ملف Excel'
  });

  return result.canceled ? null : result.filePath;
});

// معالجة إظهار رسائل التنبيه
ipcMain.handle('show-message', async (event, options) => {
  return await dialog.showMessageBox(mainWindow, options);
});
