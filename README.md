# تطبيق مخازن شركة الصناعات الكيماوية المصرية - كيما

تطبيق سطح مكتب لإدارة المخازن مصمم خصيصاً لشركة الصناعات الكيماوية المصرية - كيما. يعمل بالكامل بدون اتصال إنترنت ويدعم أنظمة Windows 7, 10, 11.

## المميزات

### 🏢 واجهة عربية احترافية
- تصميم عصري باستخدام Tailwind CSS
- دعم كامل للغة العربية (RTL)
- خطوط عربية أنيقة (Cairo)
- ألوان مريحة للعين

### 🔍 بحث ديناميكي فوري
- بحث لحظي عند كتابة أول حرف
- البحث في جميع حقول البيانات
- نتائج سريعة بدون تأخير

### 📊 إدارة شاملة للأصناف
- إضافة وتعديل وحذف الأصناف
- تتبع المواصفات والرصيد
- إدارة مواقع التخزين (الشلف والعين)
- تسجيل بيانات الموردين
- تتبع تواريخ التوريد

### 📈 إحصائيات مفيدة
- إجمالي الأصناف
- الأصناف منخفضة المخزون
- عدد الموردين
- الأصناف المضافة حديثاً

### 💾 نظام نسخ احتياطي متقدم
- إنشاء نسخ احتياطية تلقائياً
- استعادة البيانات بسهولة
- تصدير إلى Excel
- استيراد من Excel

## متطلبات النظام

- Windows 7 أو أحدث
- 4 جيجابايت رام (الحد الأدنى)
- 100 ميجابايت مساحة فارغة
- معالج 1 جيجاهرتز أو أسرع

## التثبيت والتشغيل

### للمطورين

1. **تثبيت Node.js**
   ```bash
   # تحميل Node.js من الموقع الرسمي
   https://nodejs.org/
   ```

2. **تحميل المشروع**
   ```bash
   git clone [repository-url]
   cd kima-warehouse-app
   ```

3. **تثبيت التبعيات**
   ```bash
   npm install
   ```

4. **تشغيل التطبيق في وضع التطوير**
   ```bash
   npm run dev
   ```

5. **بناء التطبيق للتوزيع**
   ```bash
   npm run build
   ```

### للمستخدمين النهائيين

1. تحميل ملف التثبيت من قسم Releases
2. تشغيل ملف التثبيت
3. اتباع تعليمات التثبيت
4. تشغيل التطبيق من قائمة ابدأ أو سطح المكتب

## استخدام التطبيق

### إضافة صنف جديد
1. انقر على زر "➕ إضافة صنف جديد"
2. املأ البيانات المطلوبة
3. انقر "حفظ"

### البحث عن صنف
1. اكتب في حقل البحث أعلى الصفحة
2. ستظهر النتائج فوراً

### تصدير البيانات
1. انقر على زر "تصدير Excel"
2. اختر مكان الحفظ
3. سيتم تحميل ملف Excel

### استيراد البيانات
1. انقر على زر "استيراد Excel"
2. اختر ملف Excel
3. سيتم استيراد البيانات تلقائياً

### إنشاء نسخة احتياطية
1. انقر على زر "نسخ احتياطي"
2. سيتم تحميل ملف JSON يحتوي على جميع البيانات

## هيكل البيانات

### جدول الأصناف
- **اسم الصنف/الرقم**: اسم الصنف أو رقمه التعريفي
- **المواصفات**: وصف تفصيلي للصنف
- **رقم الصنف المخزني**: الرقم الفريد في النظام
- **الرصيد**: الكمية المتاحة
- **رقم الشلف**: موقع الرف في المخزن
- **رقم العين**: الموقع التفصيلي داخل الرف
- **بيان الصرف**: معلومات عن استخدام الصنف
- **اسم المورد**: الشركة أو الشخص المورد
- **تاريخ التوريد**: تاريخ وصول الصنف

## الأمان والخصوصية

- جميع البيانات محفوظة محلياً على جهازك
- لا يتم إرسال أي بيانات عبر الإنترنت
- النسخ الاحتياطية مشفرة ومحمية

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:

- البريد الإلكتروني: <EMAIL>
- الهاتف: +20-xxx-xxx-xxxx
- العنوان: شركة الصناعات الكيماوية المصرية - كيما

## الترخيص

هذا التطبيق مملوك لشركة الصناعات الكيماوية المصرية - كيما.
جميع الحقوق محفوظة © 2024

## سجل التحديثات

### الإصدار 1.0.0
- الإصدار الأول من التطبيق
- واجهة عربية كاملة
- نظام إدارة المخازن الأساسي
- تصدير واستيراد Excel
- نظام النسخ الاحتياطي

## المساهمة في التطوير

نرحب بالمساهمات لتحسين التطبيق:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## شكر خاص

- فريق تطوير Electron
- مجتمع Tailwind CSS
- مطوري مكتبة XLSX
- جميع المساهمين في المشروع
