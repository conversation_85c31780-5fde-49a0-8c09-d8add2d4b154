# دليل استكشاف الأخطاء - تطبيق كيما

## مشكلة: 'electron' is not recognized

### 🐛 **الخطأ:**
```
'electron' is not recognized as an internal or external command,
operable program or batch file.
```

### 🔍 **السبب:**
electron غير مثبت أو غير متاح في PATH

### ✅ **الحلول:**

#### **الحل الأول - التثبيت التلقائي:**
```bash
# تشغيل ملف التثبيت التلقائي
install-electron.bat
```

#### **الحل الثاني - التثبيت اليدوي:**
```bash
# تثبيت electron محلياً
npm install electron --save-dev

# أو تثبيت electron عالمياً
npm install -g electron
```

#### **الحل الثالث - استخدام npx:**
```bash
# تشغيل electron باستخدام npx
npx electron .
```

#### **الحل الرابع - التشغيل المباشر:**
```bash
# إذا كان electron مثبت محلياً
node_modules\.bin\electron .

# أو
node_modules\electron\dist\electron.exe .
```

## مشكلة: EBUSY - resource busy or locked

### 🐛 **الخطأ:**
```
npm error code EBUSY
npm error syscall rename
npm error errno -4082
npm error EBUSY: resource busy or locked
```

### 🔍 **السبب:**
التطبيق أو electron يعمل في الخلفية

### ✅ **الحلول:**

#### **الحل الأول - إغلاق العمليات:**
```bash
# إغلاق جميع عمليات electron
taskkill /f /im electron.exe

# إغلاق التطبيق
taskkill /f /im "kima-warehouse-app.exe"
```

#### **الحل الثاني - إعادة تشغيل النظام:**
```bash
# إعادة تشغيل الكمبيوتر وإعادة المحاولة
```

#### **الحل الثالث - حذف node_modules:**
```bash
# حذف المجلد وإعادة التثبيت
rmdir /s node_modules
npm install
```

## مشكلة: Node.js غير مثبت

### 🐛 **الخطأ:**
```
'node' is not recognized as an internal or external command
```

### ✅ **الحل:**
1. تحميل Node.js من: https://nodejs.org
2. تثبيت الإصدار LTS
3. إعادة تشغيل Command Prompt
4. التحقق: `node --version`

## مشكلة: npm غير متاح

### 🐛 **الخطأ:**
```
'npm' is not recognized as an internal or external command
```

### ✅ **الحل:**
```bash
# npm يأتي مع Node.js، إذا لم يعمل:
# 1. إعادة تثبيت Node.js
# 2. إضافة Node.js إلى PATH يدوياً
# 3. تشغيل Command Prompt كمدير
```

## مشكلة: فشل في تثبيت التبعيات

### 🐛 **الخطأ:**
```
npm error code 1
npm error path ...
npm error command failed
```

### ✅ **الحلول:**

#### **الحل الأول - تنظيف الكاش:**
```bash
npm cache clean --force
npm install
```

#### **الحل الثاني - استخدام yarn:**
```bash
# تثبيت yarn
npm install -g yarn

# استخدام yarn بدلاً من npm
yarn install
yarn start
```

#### **الحل الثالث - تحديث npm:**
```bash
npm install -g npm@latest
```

## مشكلة: Visual Studio Build Tools مطلوبة

### 🐛 **الخطأ:**
```
gyp ERR! find VS
gyp ERR! configure error
```

### ✅ **الحل:**
1. تحميل Visual Studio Build Tools
2. تثبيت "C++ build tools"
3. إعادة تشغيل Command Prompt
4. إعادة المحاولة

## مشكلة: Python غير موجود

### 🐛 **الخطأ:**
```
gyp ERR! find Python
```

### ✅ **الحل:**
```bash
# تثبيت Python
# تحميل من: https://www.python.org/downloads/

# أو تعيين مسار Python
npm config set python python3
```

## مشكلة: التطبيق لا يفتح

### 🔍 **التحقق:**
```bash
# التحقق من وجود الملفات
dir main.js
dir src\
dir assets\

# التحقق من package.json
type package.json
```

### ✅ **الحلول:**
1. التأكد من وجود جميع الملفات
2. التحقق من main.js
3. فحص وحدة التحكم للأخطاء

## الأوامر المفيدة

### **التشخيص:**
```bash
# معلومات النظام
node --version
npm --version
electron --version

# قائمة العمليات
tasklist | findstr electron
tasklist | findstr node

# حالة المجلدات
dir node_modules\electron
dir dist\
```

### **التنظيف:**
```bash
# حذف الملفات المؤقتة
rmdir /s node_modules
del package-lock.json
npm cache clean --force

# إعادة التثبيت
npm install
```

### **التشغيل البديل:**
```bash
# طرق مختلفة للتشغيل
npm start
npx electron .
node_modules\.bin\electron .
electron .
```

## ملفات المساعدة

### **start.bat:**
- تشغيل التطبيق تلقائياً
- فحص المتطلبات
- تثبيت electron إذا لزم الأمر

### **install-electron.bat:**
- تثبيت electron فقط
- إغلاق العمليات المتضاربة
- محاولة طرق متعددة للتثبيت

### **build.bat:**
- تصدير التطبيق
- إنشاء ملف تنفيذي
- بناء النسخة المحمولة

## الدعم الفني

### **إذا استمرت المشاكل:**
1. تشغيل Command Prompt كمدير
2. التأكد من اتصال الإنترنت
3. تعطيل برامج مكافحة الفيروسات مؤقتاً
4. استخدام مجلد بدون مسافات أو أحرف عربية

### **معلومات مفيدة للدعم:**
- إصدار Windows
- إصدار Node.js
- إصدار npm
- رسالة الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة

---

**آخر تحديث:** 3 ديسمبر 2024  
**الإصدار:** 1.0.0
