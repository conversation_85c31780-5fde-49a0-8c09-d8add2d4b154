<!DOCTYPE html>
<html>
<head>
    <title>إنشاء أيقونة كيما</title>
</head>
<body>
    <canvas id="canvas" width="256" height="256"></canvas>
    <br>
    <button onclick="downloadIcon()">تحميل الأيقونة</button>
    
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // خلفية خضراء
        ctx.fillStyle = '#2D5016';
        ctx.fillRect(0, 0, 256, 256);
        
        // دائرة بيضاء في المنتصف
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.arc(128, 128, 80, 0, 2 * Math.PI);
        ctx.fill();
        
        // نص كيما
        ctx.fillStyle = '#2D5016';
        ctx.font = 'bold 48px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('كيما', 128, 140);
        
        function downloadIcon() {
            const link = document.createElement('a');
            link.download = 'icon.png';
            link.href = canvas.toDataURL();
            link.click();
        }
    </script>
</body>
</html>
