@echo off
echo ========================================
echo      تثبيت electron لتطبيق كيما
echo ========================================
echo.

echo [1/4] التحقق من Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Node.js غير مثبت
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)
echo ✓ Node.js مثبت

echo.
echo [2/4] التحقق من npm...
npm --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: npm غير متاح
    pause
    exit /b 1
)
echo ✓ npm متاح

echo.
echo [3/4] إغلاق أي عمليات electron قيد التشغيل...
taskkill /f /im electron.exe >nul 2>&1
taskkill /f /im "kima-warehouse-app.exe" >nul 2>&1
echo ✓ تم إغلاق العمليات

echo.
echo [4/4] تثبيت electron...
echo هذا قد يستغرق بضع دقائق...
echo.

REM محاولة تثبيت electron محلياً
npm install electron@27.0.0 --save-dev --force
if errorlevel 1 (
    echo.
    echo فشل التثبيت المحلي، محاولة التثبيت العالمي...
    npm install -g electron@27.0.0
    if errorlevel 1 (
        echo.
        echo فشل في تثبيت electron
        echo.
        echo الحلول المقترحة:
        echo 1. تشغيل Command Prompt كمدير
        echo 2. تثبيت Visual Studio Build Tools
        echo 3. تحديث npm: npm install -g npm@latest
        echo.
        pause
        exit /b 1
    ) else (
        echo ✓ تم تثبيت electron عالمياً
    )
) else (
    echo ✓ تم تثبيت electron محلياً
)

echo.
echo ========================================
echo        تم تثبيت electron بنجاح!
echo ========================================
echo.
echo يمكنك الآن تشغيل التطبيق بإحدى الطرق التالية:
echo 1. تشغيل start.bat
echo 2. تشغيل npm start
echo 3. تشغيل electron . (إذا كان مثبت عالمياً)
echo.
pause
