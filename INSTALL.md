# دليل التثبيت - تطبيق مخازن كيما

## للمستخدمين العاديين

### الطريقة الأولى: التثبيت السريع
1. تحميل ملف التثبيت `kima-warehouse-setup.exe`
2. تشغيل الملف كمدير (Run as Administrator)
3. اتباع تعليمات التثبيت
4. تشغيل التطبيق من قائمة ابدأ

### الطريقة الثانية: التشغيل المباشر
1. تحميل المجلد المضغوط
2. استخراج الملفات
3. النقر المزدوج على `start.bat`
4. انتظار تحميل التطبيق

## للمطورين والمتقدمين

### المتطلبات الأساسية
- Node.js (الإصدار 16 أو أحدث)
- npm (يأتي مع Node.js)
- Git (اختياري)

### خطوات التثبيت

#### 1. تحميل Node.js
```bash
# زيارة الموقع الرسمي
https://nodejs.org/

# أو استخدام Chocolatey (Windows)
choco install nodejs

# أو استخدام Winget (Windows 10/11)
winget install OpenJS.NodeJS
```

#### 2. تحميل المشروع
```bash
# باستخدام Git
git clone [repository-url]
cd kima-warehouse-app

# أو تحميل ZIP وفك الضغط
```

#### 3. تثبيت التبعيات
```bash
npm install
```

#### 4. تشغيل التطبيق
```bash
# وضع التطوير
npm run dev

# وضع الإنتاج
npm start
```

#### 5. بناء التطبيق للتوزيع
```bash
# بناء لنظام Windows
npm run build-win

# بناء عام
npm run build
```

## استكشاف الأخطاء وإصلاحها

### مشكلة: "node is not recognized"
**الحل:**
1. تأكد من تثبيت Node.js بشكل صحيح
2. أعد تشغيل Command Prompt
3. تحقق من متغيرات البيئة (PATH)

### مشكلة: "npm install fails"
**الحل:**
```bash
# مسح cache
npm cache clean --force

# حذف node_modules وإعادة التثبيت
rmdir /s node_modules
npm install

# استخدام yarn بدلاً من npm
npm install -g yarn
yarn install
```

### مشكلة: "Application won't start"
**الحل:**
1. تحقق من وجود جميع الملفات
2. تأكد من صحة package.json
3. تشغيل في وضع التطوير للحصول على تفاصيل الخطأ:
```bash
npm run dev
```

### مشكلة: "Database errors"
**الحل:**
1. مسح بيانات التطبيق:
   - Windows: `%APPDATA%/kima-warehouse-app`
2. إعادة تشغيل التطبيق
3. استعادة من نسخة احتياطية إذا متوفرة

### مشكلة: "Build fails"
**الحل:**
```bash
# تحديث electron-builder
npm install -g electron-builder

# تنظيف وإعادة البناء
npm run clean
npm run build
```

## إعدادات متقدمة

### تخصيص قاعدة البيانات
يمكن تغيير موقع قاعدة البيانات عبر تعديل `src/database.js`:
```javascript
// تغيير اسم قاعدة البيانات
this.dbName = 'customDatabaseName';
```

### تخصيص الواجهة
- تعديل الألوان في `src/styles.css`
- تغيير الخطوط في `src/index.html`
- إضافة ميزات جديدة في `src/renderer.js`

### إعدادات البناء
تعديل `package.json` في قسم `build`:
```json
{
  "build": {
    "appId": "com.yourcompany.warehouse",
    "productName": "اسم التطبيق المخصص",
    "directories": {
      "output": "dist"
    }
  }
}
```

## الأمان

### حماية البيانات
- جميع البيانات محفوظة محلياً
- لا يتم إرسال بيانات عبر الإنترنت
- النسخ الاحتياطية يجب حفظها في مكان آمن

### صلاحيات التطبيق
التطبيق يحتاج إلى:
- قراءة وكتابة الملفات المحلية
- الوصول إلى مجلد البيانات
- إنشاء وحفظ ملفات Excel

## الدعم

### الحصول على المساعدة
1. مراجعة هذا الدليل
2. البحث في Issues على GitHub
3. إنشاء Issue جديد مع تفاصيل المشكلة
4. التواصل مع فريق الدعم

### معلومات مفيدة للدعم
عند طلب المساعدة، يرجى تضمين:
- نظام التشغيل والإصدار
- إصدار Node.js (`node --version`)
- إصدار npm (`npm --version`)
- رسالة الخطأ الكاملة
- خطوات إعادة إنتاج المشكلة

## التحديثات

### تحديث التطبيق
```bash
# تحديث التبعيات
npm update

# تحديث Electron
npm install electron@latest

# إعادة بناء التطبيق
npm run build
```

### تحديث قاعدة البيانات
التطبيق يدعم ترقية قاعدة البيانات تلقائياً.
في حالة وجود مشاكل، يمكن:
1. إنشاء نسخة احتياطية
2. مسح قاعدة البيانات
3. استعادة النسخة الاحتياطية

## الخلاصة

تطبيق مخازن كيما مصمم ليكون سهل التثبيت والاستخدام.
في حالة وجود أي مشاكل، يرجى مراجعة هذا الدليل أو التواصل مع فريق الدعم.
