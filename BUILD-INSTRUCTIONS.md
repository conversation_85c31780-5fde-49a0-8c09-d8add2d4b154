# دليل تصدير تطبيق كيما - إدارة المخازن

## متطلبات التصدير

### 1. متطلبات النظام:
- **Node.js** (الإصدار 16 أو أحدث)
- **npm** (يأتي مع Node.js)
- **Git** (اختياري)

### 2. متطلبات Windows (للتصدير على Windows):
- **Visual Studio Build Tools** أو **Visual Studio Community**
- **Python** (الإصدار 3.7 أو أحدث)

## خطوات التصدير

### الطريقة الأولى: التصدير الكامل

#### 1. تثبيت المتطلبات:
```bash
# تثبيت Visual Studio Build Tools
# قم بتحميل وتثبيت Visual Studio Build Tools من:
# https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022

# تثبيت Python
# قم بتحميل وتثبيت Python من:
# https://www.python.org/downloads/
```

#### 2. تثبيت التبعيات:
```bash
# في مجلد التطبيق
npm install

# تثبيت electron-builder عالمياً
npm install -g electron-builder
```

#### 3. بناء التطبيق:
```bash
# للبناء على Windows
npm run build-win

# أو استخدام electron-builder مباشرة
electron-builder --win
```

### الطريقة الثانية: النسخة المحمولة (بدون sqlite3)

#### 1. تعديل package.json:
```json
{
  "dependencies": {
    "xlsx": "^0.18.5",
    "electron-store": "^8.1.0"
  }
}
```

#### 2. إزالة sqlite3 من الكود:
- التطبيق يستخدم IndexedDB بدلاً من sqlite3
- لا حاجة لتعديلات إضافية

#### 3. البناء:
```bash
npm install --production
electron-builder --win
```

### الطريقة الثالثة: النسخة المحمولة اليدوية

#### 1. إنشاء مجلد التوزيع:
```bash
mkdir dist
mkdir dist/kima-warehouse-app
```

#### 2. نسخ الملفات المطلوبة:
```bash
# نسخ الملفات الأساسية
cp main.js dist/kima-warehouse-app/
cp preload.js dist/kima-warehouse-app/
cp package.json dist/kima-warehouse-app/
cp -r src/ dist/kima-warehouse-app/
cp -r assets/ dist/kima-warehouse-app/

# نسخ node_modules المطلوبة فقط
cp -r node_modules/electron/ dist/kima-warehouse-app/node_modules/
cp -r node_modules/xlsx/ dist/kima-warehouse-app/node_modules/
cp -r node_modules/electron-store/ dist/kima-warehouse-app/node_modules/
```

#### 3. إنشاء ملف تشغيل:
```batch
@echo off
cd /d "%~dp0"
node_modules\electron\dist\electron.exe .
```

## إعدادات التصدير

### ملف package.json - قسم build:
```json
{
  "build": {
    "appId": "com.kima.warehouse",
    "productName": "كيما - إدارة المخازن",
    "directories": {
      "output": "dist"
    },
    "files": [
      "main.js",
      "preload.js",
      "src/**/*",
      "assets/**/*",
      "node_modules/**/*"
    ],
    "win": {
      "target": [
        {
          "target": "nsis",
          "arch": ["x64"]
        }
      ],
      "icon": "assets/kima-logo.svg"
    },
    "nsis": {
      "oneClick": false,
      "allowToChangeInstallationDirectory": true,
      "createDesktopShortcut": true,
      "createStartMenuShortcut": true,
      "shortcutName": "كيما - إدارة المخازن"
    }
  }
}
```

## حل المشاكل الشائعة

### مشكلة sqlite3:
```
خطأ: Cannot find module 'sqlite3'
```
**الحل:** إزالة sqlite3 من التبعيات واستخدام IndexedDB فقط.

### مشكلة Visual Studio:
```
خطأ: Could not find any Visual Studio installation
```
**الحل:** تثبيت Visual Studio Build Tools.

### مشكلة Python:
```
خطأ: Python not found
```
**الحل:** تثبيت Python وإضافته لمتغير PATH.

### مشكلة node-gyp:
```
خطأ: gyp ERR! configure error
```
**الحل:** 
```bash
npm install -g node-gyp
npm config set python python3
```

## الملفات الناتجة

### بعد البناء الناجح ستجد:
```
dist/
├── kima-warehouse-app Setup 1.0.0.exe    # ملف التثبيت
├── win-unpacked/                          # النسخة المحمولة
│   ├── kima-warehouse-app.exe            # الملف التنفيذي
│   ├── resources/
│   └── ...
└── builder-effective-config.yaml         # إعدادات البناء
```

## تشغيل التطبيق

### من النسخة المحمولة:
```bash
cd dist/win-unpacked
./kima-warehouse-app.exe
```

### من ملف التثبيت:
- قم بتشغيل `kima-warehouse-app Setup 1.0.0.exe`
- اتبع خطوات التثبيت
- شغل التطبيق من قائمة ابدأ أو سطح المكتب

## ملاحظات مهمة

### 1. حجم التطبيق:
- النسخة الكاملة: ~150-200 MB
- النسخة بدون sqlite3: ~120-150 MB

### 2. التوافق:
- Windows 10/11 (64-bit)
- يتطلب .NET Framework 4.5 أو أحدث

### 3. البيانات:
- يتم حفظ البيانات في IndexedDB
- مجلد البيانات: `%APPDATA%/kima-warehouse-app/`

### 4. التحديثات:
- لتحديث التطبيق، قم بإعادة البناء والتوزيع
- يمكن إضافة نظام تحديث تلقائي لاحقاً

## الدعم الفني

### في حالة مواجهة مشاكل:
1. تأكد من تثبيت جميع المتطلبات
2. احذف مجلد `node_modules` وأعد التثبيت
3. تأكد من إصدار Node.js المتوافق
4. راجع سجلات الأخطاء في وحدة التحكم

### ملفات السجل:
- Windows: `%APPDATA%/kima-warehouse-app/logs/`
- سجل البناء: `npm-debug.log`

---

**تاريخ الإنشاء:** 3 ديسمبر 2024  
**الإصدار:** 1.0.0  
**المطور:** Augment Agent
