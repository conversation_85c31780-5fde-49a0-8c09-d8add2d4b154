// نظام تسجيل الدخول لتطبيق كيما
class LoginManager {
    constructor() {
        // تحميل البيانات المحفوظة أو استخدام القيم الافتراضية
        this.loadSavedCredentials();

        this.initializeEventListeners();
        this.checkRememberedUser();
    }

    loadSavedCredentials() {
        const savedCredentials = localStorage.getItem('kimaLoginCredentials');

        if (savedCredentials) {
            try {
                const credentials = JSON.parse(savedCredentials);
                this.validUsername = credentials.username;
                this.validPassword = credentials.password;
            } catch (error) {
                console.error('خطأ في تحميل البيانات المحفوظة:', error);
                this.setDefaultCredentials();
            }
        } else {
            this.setDefaultCredentials();
        }

        this.userInfo = {
            username: this.validUsername,
            fullName: 'مدير النظام',
            role: 'مدير النظام'
        };
    }

    setDefaultCredentials() {
        this.validUsername = 'admin';
        this.validPassword = 'kima';
    }

    initializeEventListeners() {
        // نموذج تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // إظهار/إخفاء كلمة المرور
        document.getElementById('togglePassword').addEventListener('click', () => {
            this.togglePasswordVisibility();
        });

        // التحقق من الحقول عند الكتابة
        document.getElementById('username').addEventListener('input', () => {
            this.hideError();
        });

        document.getElementById('password').addEventListener('input', () => {
            this.hideError();
        });

        // تسجيل الدخول بالضغط على Enter
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleLogin();
            }
        });

        // رابط نسيت كلمة المرور
        document.getElementById('forgotPassword').addEventListener('click', (e) => {
            e.preventDefault();
            this.showForgotPasswordInfo();
        });
    }

    async handleLogin() {
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('rememberMe').checked;

        // التحقق من الحقول الفارغة
        if (!username || !password) {
            this.showError('يرجى إدخال اسم المستخدم وكلمة المرور');
            return;
        }

        // إظهار حالة التحميل
        this.setLoadingState(true);

        // محاكاة تأخير الشبكة
        await new Promise(resolve => setTimeout(resolve, 1000));

        // التحقق من بيانات المستخدم
        const user = this.authenticateUser(username, password);

        if (user) {
            // حفظ بيانات المستخدم
            this.saveUserSession(user, rememberMe);

            // إظهار رسالة نجاح
            this.showSuccess(user);

            // الانتقال للصفحة الرئيسية
            setTimeout(() => {
                this.redirectToMainApp();
            }, 1500);
        } else {
            this.setLoadingState(false);
            this.showError('بيانات تسجيل الدخول غير صحيحة');
        }
    }

    authenticateUser(username, password) {
        if (username === this.validUsername && password === this.validPassword) {
            return this.userInfo;
        }
        return null;
    }

    saveUserSession(user, rememberMe) {
        const sessionData = {
            username: user.username,
            fullName: user.fullName,
            role: user.role,
            loginTime: new Date().toISOString(),
            rememberMe: rememberMe
        };

        // حفظ في localStorage
        localStorage.setItem('kimaUserSession', JSON.stringify(sessionData));

        // حفظ في sessionStorage أيضاً
        sessionStorage.setItem('kimaCurrentUser', JSON.stringify(sessionData));

        if (rememberMe) {
            localStorage.setItem('kimaRememberedUser', username);
        } else {
            localStorage.removeItem('kimaRememberedUser');
        }
    }

    checkRememberedUser() {
        const rememberedUser = localStorage.getItem('kimaRememberedUser');
        if (rememberedUser && rememberedUser === this.validUsername) {
            const usernameInput = document.getElementById('username');
            const rememberCheckbox = document.getElementById('rememberMe');
            const passwordInput = document.getElementById('password');

            if (usernameInput && rememberCheckbox && passwordInput) {
                usernameInput.value = rememberedUser;
                rememberCheckbox.checked = true;
                passwordInput.focus();
            }
        }
    }

    togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.querySelector('#togglePassword i');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            toggleIcon.className = 'fas fa-eye-slash';
        } else {
            passwordInput.type = 'password';
            toggleIcon.className = 'fas fa-eye';
        }
    }

    showForgotPasswordInfo() {
        // إخفاء رسالة الخطأ إن وجدت
        this.hideError();

        // إنشاء نافذة تغيير كلمة المرور
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-md mx-4 shadow-xl">
                <div class="text-center mb-6">
                    <div class="mb-4">
                        <i class="fas fa-key text-green-600 text-4xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800">تغيير كلمة المرور</h3>
                    <p class="text-sm text-gray-600 mt-2">أدخل كلمة المرور الحالية ثم البيانات الجديدة</p>
                </div>

                <form id="changePasswordForm" class="space-y-4">
                    <!-- كلمة المرور الحالية -->
                    <div class="relative">
                        <input
                            type="password"
                            id="currentPassword"
                            placeholder="كلمة المرور الحالية"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
                            required
                        >
                    </div>

                    <!-- اسم المستخدم الجديد -->
                    <div class="relative">
                        <input
                            type="text"
                            id="newUsername"
                            placeholder="اسم المستخدم الجديد"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
                            required
                        >
                    </div>

                    <!-- كلمة المرور الجديدة -->
                    <div class="relative">
                        <input
                            type="password"
                            id="newPassword"
                            placeholder="كلمة المرور الجديدة"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
                            required
                        >
                    </div>

                    <!-- تأكيد كلمة المرور الجديدة -->
                    <div class="relative">
                        <input
                            type="password"
                            id="confirmNewPassword"
                            placeholder="تأكيد كلمة المرور الجديدة"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
                            required
                        >
                    </div>

                    <!-- رسالة الخطأ -->
                    <div id="changePasswordError" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg text-sm">
                        <i class="fas fa-exclamation-triangle ml-2"></i>
                        <span id="changePasswordErrorText"></span>
                    </div>

                    <!-- الأزرار -->
                    <div class="flex space-x-3 space-x-reverse pt-4">
                        <button
                            type="submit"
                            class="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg transition-colors font-medium"
                        >
                            <i class="fas fa-save ml-2"></i>
                            حفظ التغييرات
                        </button>
                        <button
                            type="button"
                            onclick="this.closest('.fixed').remove()"
                            class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg transition-colors font-medium"
                        >
                            <i class="fas fa-times ml-2"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        `;

        // إضافة النافذة للصفحة
        document.body.appendChild(modal);

        // التركيز على حقل كلمة المرور الحالية
        setTimeout(() => {
            document.getElementById('currentPassword').focus();
        }, 100);

        // معالجة نموذج تغيير كلمة المرور
        document.getElementById('changePasswordForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handlePasswordChange(modal);
        });

        // إغلاق النافذة بالنقر خارجها
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        // إغلاق النافذة بالضغط على Escape
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                modal.remove();
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);
    }

    handlePasswordChange(modal) {
        const currentPassword = document.getElementById('currentPassword').value;
        const newUsername = document.getElementById('newUsername').value.trim();
        const newPassword = document.getElementById('newPassword').value;
        const confirmNewPassword = document.getElementById('confirmNewPassword').value;

        const errorDiv = document.getElementById('changePasswordError');
        const errorText = document.getElementById('changePasswordErrorText');

        // إخفاء رسالة الخطأ
        errorDiv.classList.add('hidden');

        // التحقق من كلمة المرور الحالية
        if (currentPassword !== this.validPassword) {
            errorText.textContent = 'كلمة المرور الحالية غير صحيحة';
            errorDiv.classList.remove('hidden');
            return;
        }

        // التحقق من الحقول الفارغة
        if (!newUsername || !newPassword || !confirmNewPassword) {
            errorText.textContent = 'جميع الحقول مطلوبة';
            errorDiv.classList.remove('hidden');
            return;
        }

        // التحقق من تطابق كلمة المرور الجديدة
        if (newPassword !== confirmNewPassword) {
            errorText.textContent = 'كلمة المرور الجديدة وتأكيدها غير متطابقتين';
            errorDiv.classList.remove('hidden');
            return;
        }

        // التحقق من طول كلمة المرور الجديدة
        if (newPassword.length < 3) {
            errorText.textContent = 'كلمة المرور الجديدة يجب أن تكون 3 أحرف على الأقل';
            errorDiv.classList.remove('hidden');
            return;
        }

        // حفظ البيانات الجديدة
        this.validUsername = newUsername;
        this.validPassword = newPassword;
        this.userInfo.username = newUsername;
        this.userInfo.fullName = 'مدير النظام';

        // حفظ في localStorage للاحتفاظ بالتغييرات
        localStorage.setItem('kimaLoginCredentials', JSON.stringify({
            username: newUsername,
            password: newPassword
        }));

        // إظهار رسالة نجاح
        modal.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-md mx-4 shadow-xl">
                <div class="text-center">
                    <div class="mb-4">
                        <i class="fas fa-check-circle text-green-500 text-4xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">تم تغيير كلمة المرور بنجاح!</h3>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                        <div class="text-sm text-green-800 space-y-2">
                            <p><strong>اسم المستخدم الجديد:</strong> ${newUsername}</p>
                            <p><strong>كلمة المرور الجديدة:</strong> ${newPassword}</p>
                        </div>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">
                        استخدم البيانات الجديدة لتسجيل الدخول
                    </p>
                    <button
                        onclick="this.closest('.fixed').remove(); location.reload();"
                        class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors"
                    >
                        حسناً
                    </button>
                </div>
            </div>
        `;
    }

    setLoadingState(loading) {
        const loginBtn = document.getElementById('loginBtn');
        const loginBtnText = document.getElementById('loginBtnText');
        const loginSpinner = document.getElementById('loginSpinner');

        if (loading) {
            loginBtn.disabled = true;
            loginBtn.classList.add('opacity-75', 'cursor-not-allowed');
            loginBtnText.textContent = 'جاري تسجيل الدخول...';
            loginSpinner.classList.remove('hidden');
        } else {
            loginBtn.disabled = false;
            loginBtn.classList.remove('opacity-75', 'cursor-not-allowed');
            loginBtnText.textContent = 'تسجيل الدخول';
            loginSpinner.classList.add('hidden');
        }
    }

    showError(message) {
        const errorDiv = document.getElementById('errorMessage');
        const errorText = document.getElementById('errorText');

        errorText.textContent = message;
        errorDiv.classList.remove('hidden');

        // إضافة تأثير الاهتزاز
        errorDiv.classList.add('error-message');

        // إخفاء الرسالة بعد 5 ثوان
        setTimeout(() => {
            this.hideError();
        }, 5000);
    }

    hideError() {
        const errorDiv = document.getElementById('errorMessage');
        errorDiv.classList.add('hidden');
    }

    showSuccess(user) {
        // إخفاء رسالة الخطأ
        this.hideError();

        // تغيير نص الزر لإظهار النجاح
        const loginBtnText = document.getElementById('loginBtnText');
        const loginSpinner = document.getElementById('loginSpinner');

        loginSpinner.classList.add('hidden');
        loginBtnText.innerHTML = '<i class="fas fa-check ml-2"></i>مرحباً ' + user.fullName;

        // تغيير لون الزر
        const loginBtn = document.getElementById('loginBtn');
        loginBtn.classList.remove('login-btn');
        loginBtn.classList.add('bg-green-600');
    }

    redirectToMainApp() {
        // إعادة تحميل النافذة للانتقال للصفحة الرئيسية
        window.location.href = 'index.html';
    }

    // دالة للتحقق من حالة تسجيل الدخول
    static isLoggedIn() {
        const session = localStorage.getItem('kimaUserSession');
        return session !== null;
    }

    // دالة للحصول على بيانات المستخدم الحالي
    static getCurrentUser() {
        const session = localStorage.getItem('kimaUserSession');
        return session ? JSON.parse(session) : null;
    }

    // دالة لتسجيل الخروج
    static logout() {
        localStorage.removeItem('kimaUserSession');
        sessionStorage.removeItem('kimaCurrentUser');
        window.location.href = 'login.html';
    }
}

// تشغيل نظام تسجيل الدخول عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new LoginManager();

    // التركيز على حقل اسم المستخدم أولاً
    setTimeout(() => {
        const usernameField = document.getElementById('username');
        if (usernameField) {
            usernameField.focus();
        }
    }, 100);
});

// إضافة بعض التأثيرات البصرية
document.addEventListener('DOMContentLoaded', () => {
    // تأثير التركيز على الحقول
    const inputs = document.querySelectorAll('input[type="text"], input[type="password"]');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('ring-2', 'ring-green-500');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('ring-2', 'ring-green-500');
        });
    });

    // تأثير الكتابة على العنوان
    const title = document.querySelector('h1');
    if (title) {
        title.style.opacity = '0';
        title.style.transform = 'translateY(20px)';

        setTimeout(() => {
            title.style.transition = 'all 0.8s ease';
            title.style.opacity = '1';
            title.style.transform = 'translateY(0)';
        }, 300);
    }
});
