// نظام تسجيل الدخول البسيط - تطبيق كيما
class SimpleLoginManager {
    constructor() {
        // بيانات تسجيل الدخول الافتراضية
        this.validUsername = 'admin';
        this.validPassword = 'kima';

        // تحميل كلمة المرور المحفوظة إن وجدت
        this.loadSavedPassword();

        this.initializeEventListeners();
        this.checkRememberedUser();
        this.focusOnUsername();
    }

    loadSavedPassword() {
        const savedPassword = localStorage.getItem('kimaPassword');
        if (savedPassword) {
            this.validPassword = savedPassword;
        }
    }

    initializeEventListeners() {
        // نموذج تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // إظهار/إخفاء كلمة المرور
        document.getElementById('togglePassword').addEventListener('click', () => {
            this.togglePasswordVisibility();
        });

        // إخفاء رسالة الخطأ عند الكتابة
        document.getElementById('username').addEventListener('input', () => {
            this.hideError();
        });

        document.getElementById('password').addEventListener('input', () => {
            this.hideError();
        });

        // تسجيل الدخول بالضغط على Enter
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleLogin();
            }
        });

        // زر نسيت كلمة المرور
        document.getElementById('forgotPassword').addEventListener('click', (e) => {
            e.preventDefault();
            this.showChangePasswordModal();
        });
    }

    async handleLogin() {
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('rememberMe').checked;

        // التحقق من الحقول الفارغة
        if (!username || !password) {
            this.showError('يرجى إدخال اسم المستخدم وكلمة المرور');
            return;
        }

        // إظهار حالة التحميل
        this.setLoadingState(true);

        // محاكاة تأخير الشبكة
        await new Promise(resolve => setTimeout(resolve, 1000));

        // التحقق من بيانات المستخدم
        if (username === this.validUsername && password === this.validPassword) {
            // حفظ بيانات المستخدم
            this.saveUserSession(username, rememberMe);
            
            // إظهار رسالة نجاح
            this.showSuccess();
            
            // الانتقال للصفحة الرئيسية
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);
        } else {
            this.setLoadingState(false);
            this.showError('اسم المستخدم أو كلمة المرور غير صحيحة');
        }
    }

    saveUserSession(username, rememberMe) {
        const sessionData = {
            username: username,
            fullName: 'مدير النظام',
            role: 'مدير النظام',
            loginTime: new Date().toISOString(),
            rememberMe: rememberMe
        };

        // حفظ في localStorage
        localStorage.setItem('kimaUserSession', JSON.stringify(sessionData));

        // حفظ في sessionStorage أيضاً
        sessionStorage.setItem('kimaCurrentUser', JSON.stringify(sessionData));

        // حفظ بيانات "تذكرني"
        if (rememberMe) {
            const rememberData = {
                username: username,
                password: this.validPassword,
                savedAt: new Date().toISOString()
            };
            localStorage.setItem('kimaRememberedData', JSON.stringify(rememberData));
        } else {
            // إزالة البيانات المحفوظة إذا لم يتم اختيار "تذكرني"
            localStorage.removeItem('kimaRememberedData');
            localStorage.removeItem('kimaRememberedUser'); // للتوافق مع النسخة القديمة
        }
    }

    checkRememberedUser() {
        const rememberedData = localStorage.getItem('kimaRememberedData');
        if (rememberedData) {
            try {
                const data = JSON.parse(rememberedData);
                const usernameInput = document.getElementById('username');
                const passwordInput = document.getElementById('password');
                const rememberCheckbox = document.getElementById('rememberMe');

                if (usernameInput && passwordInput && rememberCheckbox) {
                    usernameInput.value = data.username || '';
                    passwordInput.value = data.password || '';
                    rememberCheckbox.checked = true;

                    // التركيز على زر تسجيل الدخول إذا كانت البيانات محفوظة
                    if (data.username && data.password) {
                        document.getElementById('loginBtn').focus();
                    } else if (data.username && !data.password) {
                        passwordInput.focus();
                    } else {
                        usernameInput.focus();
                    }
                }
            } catch (error) {
                console.error('خطأ في تحميل البيانات المحفوظة:', error);
                localStorage.removeItem('kimaRememberedData');
            }
        }
    }

    focusOnUsername() {
        setTimeout(() => {
            const usernameField = document.getElementById('username');
            const passwordField = document.getElementById('password');
            const loginBtn = document.getElementById('loginBtn');

            // إذا كانت البيانات محفوظة، التركيز على زر تسجيل الدخول
            if (usernameField && passwordField && usernameField.value && passwordField.value) {
                loginBtn.focus();
            }
            // إذا كان اسم المستخدم محفوظ فقط، التركيز على كلمة المرور
            else if (usernameField && usernameField.value && (!passwordField || !passwordField.value)) {
                passwordField.focus();
            }
            // إذا لم تكن هناك بيانات محفوظة، التركيز على اسم المستخدم
            else if (usernameField && !usernameField.value) {
                usernameField.focus();
            }
        }, 200);
    }

    togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.querySelector('#togglePassword i');
        const toggleButton = document.getElementById('togglePassword');

        if (passwordInput.type === 'password') {
            // كلمة المرور مخفية حالياً، سنقوم بإظهارها
            passwordInput.type = 'text';
            toggleIcon.className = 'fas fa-eye'; // عين مفتوحة = كلمة المرور ظاهرة
            toggleButton.title = 'إخفاء كلمة المرور';
        } else {
            // كلمة المرور ظاهرة حالياً، سنقوم بإخفائها
            passwordInput.type = 'password';
            toggleIcon.className = 'fas fa-eye-slash'; // عين مغلقة = كلمة المرور مخفية
            toggleButton.title = 'إظهار كلمة المرور';
        }
    }

    showChangePasswordModal() {
        // إخفاء رسالة الخطأ إن وجدت
        this.hideError();

        // إنشاء نافذة تغيير كلمة المرور
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white rounded-2xl p-6 max-w-md mx-4 shadow-xl">
                <div class="text-center mb-6">
                    <div class="mb-4">
                        <i class="fas fa-key text-green-600 text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800">تغيير كلمة المرور</h3>
                    <p class="text-sm text-gray-600 mt-2">أدخل كلمة المرور الحالية ثم كلمة المرور الجديدة</p>
                </div>

                <form id="changePasswordForm" class="space-y-4">
                    <!-- كلمة المرور الحالية -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-lock ml-2"></i>
                            كلمة المرور الحالية
                        </label>
                        <input
                            type="password"
                            id="currentPassword"
                            placeholder="أدخل كلمة المرور الحالية"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-green-600 outline-none"
                            required
                        >
                    </div>

                    <!-- كلمة المرور الجديدة -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-key ml-2"></i>
                            كلمة المرور الجديدة
                        </label>
                        <input
                            type="password"
                            id="newPassword"
                            placeholder="أدخل كلمة المرور الجديدة"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-green-600 outline-none"
                            required
                        >
                    </div>

                    <!-- تأكيد كلمة المرور الجديدة -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-check ml-2"></i>
                            تأكيد كلمة المرور الجديدة
                        </label>
                        <input
                            type="password"
                            id="confirmNewPassword"
                            placeholder="أعد إدخال كلمة المرور الجديدة"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-green-600 outline-none"
                            required
                        >
                    </div>

                    <!-- رسالة الخطأ -->
                    <div id="changePasswordError" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg text-sm">
                        <i class="fas fa-exclamation-triangle ml-2"></i>
                        <span id="changePasswordErrorText"></span>
                    </div>

                    <!-- الأزرار -->
                    <div class="flex space-x-3 space-x-reverse pt-4">
                        <button
                            type="submit"
                            class="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg transition-colors font-medium"
                        >
                            <i class="fas fa-save ml-2"></i>
                            حفظ التغييرات
                        </button>
                        <button
                            type="button"
                            onclick="this.closest('.fixed').remove()"
                            class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-3 rounded-lg transition-colors font-medium"
                        >
                            <i class="fas fa-times ml-2"></i>
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        `;

        // إضافة النافذة للصفحة
        document.body.appendChild(modal);

        // التركيز على حقل كلمة المرور الحالية
        setTimeout(() => {
            document.getElementById('currentPassword').focus();
        }, 100);

        // معالجة نموذج تغيير كلمة المرور
        document.getElementById('changePasswordForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handlePasswordChange(modal);
        });

        // إغلاق النافذة بالنقر خارجها
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });

        // إغلاق النافذة بالضغط على Escape
        const handleEscape = (e) => {
            if (e.key === 'Escape') {
                modal.remove();
                document.removeEventListener('keydown', handleEscape);
            }
        };
        document.addEventListener('keydown', handleEscape);
    }

    handlePasswordChange(modal) {
        const currentPassword = document.getElementById('currentPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmNewPassword = document.getElementById('confirmNewPassword').value;

        const errorDiv = document.getElementById('changePasswordError');
        const errorText = document.getElementById('changePasswordErrorText');

        // إخفاء رسالة الخطأ
        errorDiv.classList.add('hidden');

        // التحقق من كلمة المرور الحالية
        if (currentPassword !== this.validPassword) {
            errorText.textContent = 'كلمة المرور الحالية غير صحيحة';
            errorDiv.classList.remove('hidden');
            return;
        }

        // التحقق من الحقول الفارغة
        if (!newPassword || !confirmNewPassword) {
            errorText.textContent = 'جميع الحقول مطلوبة';
            errorDiv.classList.remove('hidden');
            return;
        }

        // التحقق من تطابق كلمة المرور الجديدة
        if (newPassword !== confirmNewPassword) {
            errorText.textContent = 'كلمة المرور الجديدة وتأكيدها غير متطابقتين';
            errorDiv.classList.remove('hidden');
            return;
        }

        // التحقق من طول كلمة المرور الجديدة
        if (newPassword.length < 3) {
            errorText.textContent = 'كلمة المرور الجديدة يجب أن تكون 3 أحرف على الأقل';
            errorDiv.classList.remove('hidden');
            return;
        }

        // حفظ كلمة المرور الجديدة
        this.validPassword = newPassword;

        // حفظ في localStorage للاحتفاظ بالتغييرات
        localStorage.setItem('kimaPassword', newPassword);

        // إظهار رسالة نجاح
        modal.innerHTML = `
            <div class="bg-white rounded-2xl p-6 max-w-md mx-4 shadow-xl">
                <div class="text-center">
                    <div class="mb-4">
                        <i class="fas fa-check-circle text-green-500 text-4xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-4">تم تغيير كلمة المرور بنجاح!</h3>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                        <div class="text-sm text-green-800">
                            <p><strong>كلمة المرور الجديدة:</strong> ${newPassword}</p>
                        </div>
                    </div>
                    <p class="text-sm text-gray-600 mb-4">
                        استخدم كلمة المرور الجديدة لتسجيل الدخول
                    </p>
                    <button
                        onclick="this.closest('.fixed').remove();"
                        class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors"
                    >
                        حسناً
                    </button>
                </div>
            </div>
        `;
    }

    setLoadingState(loading) {
        const loginBtn = document.getElementById('loginBtn');
        const loginBtnText = document.getElementById('loginBtnText');
        const loginSpinner = document.getElementById('loginSpinner');

        if (loading) {
            loginBtn.disabled = true;
            loginBtn.classList.add('opacity-75', 'cursor-not-allowed');
            loginBtnText.textContent = 'جاري تسجيل الدخول...';
            loginSpinner.classList.remove('hidden');
        } else {
            loginBtn.disabled = false;
            loginBtn.classList.remove('opacity-75', 'cursor-not-allowed');
            loginBtnText.textContent = 'تسجيل الدخول';
            loginSpinner.classList.add('hidden');
        }
    }

    showError(message) {
        const errorDiv = document.getElementById('errorMessage');
        const errorText = document.getElementById('errorText');
        
        errorText.textContent = message;
        errorDiv.classList.remove('hidden');
        
        // إخفاء الرسالة بعد 5 ثوان
        setTimeout(() => {
            this.hideError();
        }, 5000);
    }

    hideError() {
        const errorDiv = document.getElementById('errorMessage');
        errorDiv.classList.add('hidden');
    }

    showSuccess() {
        // إخفاء رسالة الخطأ
        this.hideError();
        
        // تغيير نص الزر لإظهار النجاح
        const loginBtnText = document.getElementById('loginBtnText');
        const loginSpinner = document.getElementById('loginSpinner');
        
        loginSpinner.classList.add('hidden');
        loginBtnText.innerHTML = '<i class="fas fa-check ml-2"></i>مرحباً بك';
        
        // تغيير لون الزر
        const loginBtn = document.getElementById('loginBtn');
        loginBtn.classList.remove('btn-login');
        loginBtn.classList.add('bg-green-600');
    }

    // دالة للتحقق من حالة تسجيل الدخول
    static isLoggedIn() {
        const session = localStorage.getItem('kimaUserSession');
        return session !== null;
    }

    // دالة للحصول على بيانات المستخدم الحالي
    static getCurrentUser() {
        const session = localStorage.getItem('kimaUserSession');
        return session ? JSON.parse(session) : null;
    }

    // دالة لتسجيل الخروج
    static logout() {
        localStorage.removeItem('kimaUserSession');
        sessionStorage.removeItem('kimaCurrentUser');
        // لا نحذف kimaRememberedData هنا للاحتفاظ بميزة "تذكرني"
        window.location.href = 'login.html';
    }

    // دالة لمسح جميع البيانات المحفوظة
    static clearAllData() {
        localStorage.removeItem('kimaUserSession');
        sessionStorage.removeItem('kimaCurrentUser');
        localStorage.removeItem('kimaRememberedData');
        localStorage.removeItem('kimaRememberedUser');
        localStorage.removeItem('kimaPassword');
        window.location.href = 'login.html';
    }
}

// تشغيل نظام تسجيل الدخول عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new SimpleLoginManager();
});

// إضافة تأثيرات بصرية
document.addEventListener('DOMContentLoaded', () => {
    // تأثير الكتابة على العنوان
    const title = document.querySelector('h1');
    if (title) {
        title.style.opacity = '0';
        title.style.transform = 'translateY(20px)';

        setTimeout(() => {
            title.style.transition = 'all 0.8s ease';
            title.style.opacity = '1';
            title.style.transform = 'translateY(0)';
        }, 300);
    }
});
