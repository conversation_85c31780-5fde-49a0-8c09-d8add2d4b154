// نظام تسجيل الدخول البسيط - تطبيق كيما
class SimpleLoginManager {
    constructor() {
        // بيانات تسجيل الدخول الافتراضية
        this.validUsername = 'admin';
        this.validPassword = 'kima';
        
        this.initializeEventListeners();
        this.checkRememberedUser();
        this.focusOnUsername();
    }

    initializeEventListeners() {
        // نموذج تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // إظهار/إخفاء كلمة المرور
        document.getElementById('togglePassword').addEventListener('click', () => {
            this.togglePasswordVisibility();
        });

        // إخفاء رسالة الخطأ عند الكتابة
        document.getElementById('username').addEventListener('input', () => {
            this.hideError();
        });

        document.getElementById('password').addEventListener('input', () => {
            this.hideError();
        });

        // تسجيل الدخول بالضغط على Enter
        document.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleLogin();
            }
        });
    }

    async handleLogin() {
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('rememberMe').checked;

        // التحقق من الحقول الفارغة
        if (!username || !password) {
            this.showError('يرجى إدخال اسم المستخدم وكلمة المرور');
            return;
        }

        // إظهار حالة التحميل
        this.setLoadingState(true);

        // محاكاة تأخير الشبكة
        await new Promise(resolve => setTimeout(resolve, 1000));

        // التحقق من بيانات المستخدم
        if (username === this.validUsername && password === this.validPassword) {
            // حفظ بيانات المستخدم
            this.saveUserSession(username, rememberMe);
            
            // إظهار رسالة نجاح
            this.showSuccess();
            
            // الانتقال للصفحة الرئيسية
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1500);
        } else {
            this.setLoadingState(false);
            this.showError('اسم المستخدم أو كلمة المرور غير صحيحة');
        }
    }

    saveUserSession(username, rememberMe) {
        const sessionData = {
            username: username,
            fullName: 'مدير النظام',
            role: 'مدير النظام',
            loginTime: new Date().toISOString(),
            rememberMe: rememberMe
        };

        // حفظ في localStorage
        localStorage.setItem('kimaUserSession', JSON.stringify(sessionData));

        // حفظ في sessionStorage أيضاً
        sessionStorage.setItem('kimaCurrentUser', JSON.stringify(sessionData));

        if (rememberMe) {
            localStorage.setItem('kimaRememberedUser', username);
        } else {
            localStorage.removeItem('kimaRememberedUser');
        }
    }

    checkRememberedUser() {
        const rememberedUser = localStorage.getItem('kimaRememberedUser');
        if (rememberedUser) {
            const usernameInput = document.getElementById('username');
            const rememberCheckbox = document.getElementById('rememberMe');
            const passwordInput = document.getElementById('password');
            
            if (usernameInput && rememberCheckbox && passwordInput) {
                usernameInput.value = rememberedUser;
                rememberCheckbox.checked = true;
                passwordInput.focus();
            }
        }
    }

    focusOnUsername() {
        setTimeout(() => {
            const usernameField = document.getElementById('username');
            if (usernameField && !usernameField.value) {
                usernameField.focus();
            }
        }, 100);
    }

    togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const toggleIcon = document.querySelector('#togglePassword i');
        const toggleButton = document.getElementById('togglePassword');

        if (passwordInput.type === 'password') {
            // كلمة المرور مخفية حالياً، سنقوم بإظهارها
            passwordInput.type = 'text';
            toggleIcon.className = 'fas fa-eye'; // عين مفتوحة = كلمة المرور ظاهرة
            toggleButton.title = 'إخفاء كلمة المرور';
        } else {
            // كلمة المرور ظاهرة حالياً، سنقوم بإخفائها
            passwordInput.type = 'password';
            toggleIcon.className = 'fas fa-eye-slash'; // عين مغلقة = كلمة المرور مخفية
            toggleButton.title = 'إظهار كلمة المرور';
        }
    }

    setLoadingState(loading) {
        const loginBtn = document.getElementById('loginBtn');
        const loginBtnText = document.getElementById('loginBtnText');
        const loginSpinner = document.getElementById('loginSpinner');

        if (loading) {
            loginBtn.disabled = true;
            loginBtn.classList.add('opacity-75', 'cursor-not-allowed');
            loginBtnText.textContent = 'جاري تسجيل الدخول...';
            loginSpinner.classList.remove('hidden');
        } else {
            loginBtn.disabled = false;
            loginBtn.classList.remove('opacity-75', 'cursor-not-allowed');
            loginBtnText.textContent = 'تسجيل الدخول';
            loginSpinner.classList.add('hidden');
        }
    }

    showError(message) {
        const errorDiv = document.getElementById('errorMessage');
        const errorText = document.getElementById('errorText');
        
        errorText.textContent = message;
        errorDiv.classList.remove('hidden');
        
        // إخفاء الرسالة بعد 5 ثوان
        setTimeout(() => {
            this.hideError();
        }, 5000);
    }

    hideError() {
        const errorDiv = document.getElementById('errorMessage');
        errorDiv.classList.add('hidden');
    }

    showSuccess() {
        // إخفاء رسالة الخطأ
        this.hideError();
        
        // تغيير نص الزر لإظهار النجاح
        const loginBtnText = document.getElementById('loginBtnText');
        const loginSpinner = document.getElementById('loginSpinner');
        
        loginSpinner.classList.add('hidden');
        loginBtnText.innerHTML = '<i class="fas fa-check ml-2"></i>مرحباً بك';
        
        // تغيير لون الزر
        const loginBtn = document.getElementById('loginBtn');
        loginBtn.classList.remove('btn-login');
        loginBtn.classList.add('bg-green-600');
    }

    // دالة للتحقق من حالة تسجيل الدخول
    static isLoggedIn() {
        const session = localStorage.getItem('kimaUserSession');
        return session !== null;
    }

    // دالة للحصول على بيانات المستخدم الحالي
    static getCurrentUser() {
        const session = localStorage.getItem('kimaUserSession');
        return session ? JSON.parse(session) : null;
    }

    // دالة لتسجيل الخروج
    static logout() {
        localStorage.removeItem('kimaUserSession');
        sessionStorage.removeItem('kimaCurrentUser');
        window.location.href = 'login.html';
    }
}

// تشغيل نظام تسجيل الدخول عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    new SimpleLoginManager();
});

// إضافة تأثيرات بصرية
document.addEventListener('DOMContentLoaded', () => {
    // تأثير الكتابة على العنوان
    const title = document.querySelector('h1');
    if (title) {
        title.style.opacity = '0';
        title.style.transform = 'translateY(20px)';

        setTimeout(() => {
            title.style.transition = 'all 0.8s ease';
            title.style.opacity = '1';
            title.style.transform = 'translateY(0)';
        }, 300);
    }
});
